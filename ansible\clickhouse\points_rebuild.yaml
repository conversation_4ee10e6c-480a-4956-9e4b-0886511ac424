# ap clickhouse/points_rebuild --extra-vars="host=points_czmp"
# ap clickhouse/points_rebuild --extra-vars="host=p1"

- name: rebuild clickhouse database
  hosts: "{{ host }}"
  tasks:
    - name: DROP database
      shell: |
        clickhouse-client --password CZMP.2021 --port 9030 -nq "DROP DATABASE cityserver_mater_pg_point"
      ignore_errors: true
    - name: CREATE database
      shell: >
        clickhouse-client --password CZMP.2021 --port 9030 -nq "
        SET allow_experimental_database_materialized_postgresql=1;
        CREATE DATABASE cityserver_mater_pg_point
        ENGINE = MaterializedPostgreSQL('127.0.0.1:5432', 'czmppoint', 'postgres', 'CZMP.2019')
        SETTINGS
        materialized_postgresql_tables_list = 'bs_dd_check_weight,alerting',
        materialized_postgresql_max_block_size=1;
        "
    - name: SHOW TABLES
      shell: |
        clickhouse-client --password CZMP.2021 --port 9030 -nq "SHOW TABLES FROM cityserver_mater_pg_point"
      register: show
    - name: std
      debug: msg="{{ show.stderr }} {{ show.stdout }}"