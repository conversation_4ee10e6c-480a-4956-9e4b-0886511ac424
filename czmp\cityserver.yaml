apiVersion: apps/v1
kind: Deployment
metadata:
  name: cityserver-deployment
  labels:
    platform: city
    kind: server
spec:
  replicas: 2
  selector:
    matchLabels:
      app: spring
  template:
    metadata:
      labels:
        app: spring
    spec:
      terminationGracePeriodSeconds: 12
      imagePullSecrets:
        - name: regcred
      hostAliases:
        - ip: "*************"
          hostnames:
            - "www.liquibase.org"
      containers:
        - name: cityserver
          image: registry.cn-shanghai.aliyuncs.com/czmp/czmpcityserver
          # imagePullPolicy: "Always" # if use lastest
          imagePullPolicy: "IfNotPresent"
          resources:
            limits:
              memory: 16G
              cpu: 8
          ports:
            - containerPort: 8080
          envFrom:
            - configMapRef:
                name: cityserver-envs
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: prod,api-docs
          # https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
          readinessProbe:
            httpGet:
              path: /management/health
              port: 8080
            initialDelaySeconds: 6
            periodSeconds: 3
          volumeMounts:
            - name: system-fonts
              mountPath: /usr/share/fonts
      volumes:
        - name: system-fonts
          hostPath:
            path: /usr/share/fonts
            type: Directory
