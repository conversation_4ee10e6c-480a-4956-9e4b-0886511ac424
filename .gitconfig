# cp .gitconfig ~\.gitconfig

[user]
	email = zic<PERSON>@gmail.com
	name = <PERSON>

[alias]
	st = status -sb
	com = commit -a -m 'temp'
  unstage = reset HEAD --
	pgg = git push && git push gh
	acp = git add . | git commit -a -m '.' | git push

[core]
	autocrlf = input
	ignorecase = false
	longpaths = true
	filemode = false

[pull]
	ff = only

[i18n]
	commitencoding = utf-8
  logoutputencoding = utf-8

[init]
	defaultBranch = main

[filter "lfs"]
	smudge = git-lfs smudge -- %f
	process = git-lfs filter-process
	required = true
	clean = git-lfs clean -- %f

[credential]
	helper = store

# [http]
# 	proxy = socks5://127.0.0.1:7892
# [http "https://github.com"]
# 	proxy = socks5://127.0.0.1:7892
