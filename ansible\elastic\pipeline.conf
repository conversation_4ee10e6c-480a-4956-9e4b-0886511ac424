input {
  beats {
    port => 5044
  }

  # https://www.jhipster.tech/monitoring/#forwarding-logs-to-logstash
  tcp {
    port => 5000
    type => syslog
    codec => json_lines
  }
}

## Add your filters / logstash plugins configuration here
## todo: https://www.elastic.co/guide/en/logstash/current/plugins-outputs-elasticsearch.html#_writing_to_different_indices_best_practices

output {
  elasticsearch {
    hosts => "${ELASTIC_HOSTS:-host.docker.internal:9200}"
    index => "platform-%{+YYYY.MM.dd}"
    user => "elastic"
    password => "${ELASTIC_PASSWORD}"
    ecs_compatibility => disabled
  }
}