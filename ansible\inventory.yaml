# ansible-inventory -i inventory.yaml --list
# ansible cloud -m ping -i inventory.yaml

temp:
  hosts:
    temp72:
      ansible_host: temp72
    fm:
      ansible_host: fm

office:
  hosts:
    of:
      ansible_host: of # ofc

cloud:
  hosts:
    y0:
      ansible_host: y0
    y1:
      ansible_host: y1
    y2:
      ansible_host: y2
    y3:
      ansible_host: y3
    y4:
      ansible_host: y4
    y5:
      ansible_host: y5
    y6:
      ansible_host: y6

media:
  hosts:
    m1:
      ansible_host: m1
    m2:
      ansible_host: m2
    m3:
      ansible_host: m3

city:
  children:
    cloud:
    media:

city_citus:
  hosts:
    y3:
      ansible_host: y3
    y4:
      ansible_host: y4
    y5:
      ansible_host: y5
    y6:
      ansible_host: y6

city_clickhouse:
  hosts:
    y2:
      ansible_host: y2
    y3:
      ansible_host: y3
    y5:
      ansible_host: y5
    y6:
      ansible_host: y6

city_elastic:
  hosts:
    y1:
      ansible_host: y1

jintan:
  hosts:
    j1:
      ansible_host: j1
    j2:
      ansible_host: j2
    j3:
      ansible_host: j3

jintan_pg:
  hosts:
    j2:
      ansible_host: j2

jintan_clickhouse:
  hosts:
    j2:
      ansible_host: j2

jintan_elastic:
  hosts:
    j1:
      ansible_host: j1

liyang:
  hosts:
    l1:
      ansible_host: l1
    l2:
      ansible_host: l2
    l3:
      ansible_host: l3
    l4:
      ansible_host: l4

liyang_hub:
  hosts:
    l2:
      ansible_host: l2
    l3:
      ansible_host: l3
    l4:
      ansible_host: l4

liyang_pg:
  hosts:
    l3:
      ansible_host: l3

liyang_clickhouse:
  hosts:
    l4:
      ansible_host: l4

liyang_elastic:
  hosts:
    j1:
      ansible_host: j1

xinbei:
  hosts:
    xb:
      ansible_host: xb

wujin:
  hosts:
    wj:
      ansible_host: wj

hub_area:
  children:
    liyang_hub:
  hosts:
    j3:
      ansible_host: j3
    xb:
      ansible_host: xb
    wj:
      ansible_host: wj

points_jintan:
  hosts:
    p1:
      ansible_host: p1
    p2:
      ansible_host: p2
    p3:
      ansible_host: p3
    p4:
      ansible_host: p4
    p75:
      ansible_host: p75

points_wujin:
  hosts:
    p5:
      ansible_host: p5
    p6:
      ansible_host: p6
    p7:
      ansible_host: p7
    p8:
      ansible_host: p8

points_xinbei:
  hosts:
    p11:
      ansible_host: p11
    p12:
      ansible_host: p12
    p13:
      ansible_host: p13
    p14:
      ansible_host: p14
    p15:
      ansible_host: p15

points_bridge:
  hosts:
    p21:
      ansible_host: p21
    p22:
      ansible_host: p22
    p23:
      ansible_host: p23

points_taizhou:
  hosts:
    p30:
      ansible_host: p30
    p31:
      ansible_host: p31
    p32:
      ansible_host: p32

points_czmp:
  children:
    points_jintan:
    points_wujin:
    points_xinbei:
    points_bridge:

points:
  children:
    points_jintan:
    points_wujin:
    points_xinbei:
    points_bridge:
    points_taizhou:
  hosts:
    p20:
      ansible_host: p20
    p26:
      ansible_host: p26
    p27:
      ansible_host: p27
    p41:
      ansible_host: p41

grafana_master:
  hosts:
    p1_root:
      ansible_host: p1

grafana_nodes:
  hosts:
    p2:
      ansible_host: p2
    p3:
      ansible_host: p3
    p4:
      ansible_host: p4
  children:
    points_wujin:
    points_xinbei:

all_hosts:
  children:
    city:
    jintan:
    liyang:
    points:
