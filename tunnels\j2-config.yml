tunnel: 6464edd7-c2a6-4379-be93-02d8df89da8a
credentials-file: /home/<USER>/.cloudflared/6464edd7-c2a6-4379-be93-02d8df89da8a.json
protocol: auto
ingress:
  - hostname: jt2-ssh.socode.top
    service: ssh://localhost:22
  - hostname: jt2-api.socode.top
    service: http://localhost:8235
  - hostname: jt2-api-flow.socode.top
    service: http://localhost:9132
  - hostname: jt2-kibana.socode.top
    service: http://localhost:5601
  - hostname: jt2-kafka-87.socode.top
    service: http://localhost:8087
  - hostname: jt2-pg.socode.top
    service: tcp://localhost:5432
  - hostname: jt2-clickhouse.socode.top
    service: http://localhost:8123
  - hostname: jt2-minio.socode.top
    service: http://localhost:7300
  - hostname: jt2-minio-console.socode.top
    service: http://localhost:7301
  - hostname: jt2-grafana.socode.top
    service: http://localhost:3080
  - hostname: jt2-web.socode.top
    service: http://localhost:16002
  - service: http_status:404

# cloudflared tunnel route dns jt2 jt2-ssh.socode.top
# cloudflared tunnel route dns jt2 jt2-api.socode.top
# cloudflared tunnel route dns jt2 jt2-api-flow.socode.top
# cloudflared tunnel route dns jt2 jt2-web.socode.top
# cloudflared tunnel route dns jt2 jt2-kibana.socode.top
# cloudflared tunnel route dns jt2 jt2-kafka-87.socode.top
# cloudflared tunnel route dns jt2 jt2-pg.socode.top
# cloudflared tunnel route dns jt2 jt2-clickhouse.socode.top
# cloudflared tunnel route dns jt2 jt2-minio.socode.top
# cloudflared tunnel route dns jt2 jt2-minio-console.socode.top
# cloudflared tunnel route dns jt2 jt2-grafana.socode.top

# cloudflared --loglevel debug --transport-loglevel warn --config /etc/cloudflared/config.yml tunnel run 6464edd7-c2a6-4379-be93-02d8df89da8a

