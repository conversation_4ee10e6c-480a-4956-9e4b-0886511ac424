{"log": {"loglevel": "warning", "access": "/var/log/v2ray/access.log", "error": "/var/log/v2ray/error.log"}, "routing": {"domainStrategy": "AsIs", "rules": [{"type": "field", "ip": ["geoip:private"], "outboundTag": "block"}]}, "inbounds": [{"listen": "0.0.0.0", "port": 433, "protocol": "vmess", "settings": {"clients": [{"id": "08ea6cfc-c76b-11ec-9d64-0242ac120003", "alterId": 64}]}, "streamSettings": {"network": "ws", "security": "tls", "tlsSettings": {"certificates": [{"certificateFile": "/var/www/fullchain.cer", "keyFile": "/var/www/tokyo.windcss.com.key"}]}}}], "outbounds": [{"protocol": "freedom", "tag": "direct"}, {"protocol": "blackhole", "tag": "block"}]}