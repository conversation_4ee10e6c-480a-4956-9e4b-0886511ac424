# wget https://raw.githubusercontent.com/grafana/loki/main/clients/cmd/promtail/promtail-local-config.yaml -o promtail-config.yaml

server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://host.docker.internal:3100/loki/api/v1/push

scrape_configs:
- job_name: quartz
  static_configs:
  - targets:
      - localhost
    labels:
      job: quartz
      __path__: /var/log/quartz-driver/*.log
      stream: stdout
  pipeline_stages:
    - regex:
        # https://community.grafana.com/t/parsing-timestamp-from-logline-with-promtail-and-sending-to-loki/85381/4
       expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z) (?P<level>\[ERROR\]|\[WARN\]|\[INFO\]|\[DEBUG\]) (?P<message>.+)$'
    - labels:
        timestamp: timestamp
        level: level
        message: message
    - timestamp:
        source: timestamp
        format: RFC3339Nano
