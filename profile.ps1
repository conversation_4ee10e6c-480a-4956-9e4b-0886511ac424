# cp profile.ps1 C:\Users\<USER>\Documents\PowerShell\Microsoft.PowerShell_profile.ps1

# Import vibe.ps1 functions and aliases
$vibeScript = "$PSScriptRoot\vibe.ps1"
if (Test-Path $vibeScript) {
  . $vibeScript
}
else {
  Write-Warning "vibe.ps1 not found at $vibeScript"
}

# Shows navigable menu of all options when hitting Tab
Set-PSReadlineKeyHandler -Key Tab -Function MenuComplete

# Autocompletion for arrow keys
Set-PSReadlineKeyHandler -Key UpArrow -Function HistorySearchBackward
Set-PSReadlineKeyHandler -Key DownArrow -Function HistorySearchForward

# Chocolatey profile
$ChocolateyProfile = "$env:ChocolateyInstall\helpers\chocolateyProfile.psm1"
if (Test-Path($ChocolateyProfile)) {
  Import-Module "$ChocolateyProfile"
}

# 需要重启 terminal
function proxyon { 
  setx HTTPS_PROXY 127.0.0.1:1088
  setx HTTP_PROXY 127.0.0.1:1088
}

function proxyoff { 
  setx HTTP_PROXY=null
  setx HTTPS_PROXY=null
}

function gitelr { 
  git config user.name el<PERSON><PERSON><PERSON>;
  git config user.email ellio<PERSON><EMAIL>;
}

function gitzic { 
  git config user.name zicjin;
  git config user.email <EMAIL>;
}

function gs([string]$branch) {
  git switch $branch
}

function vdl([string]$Url) {
  yt-dlp --cookies-from-browser edge --proxy socks5://127.0.0.1:7892 "$Url"
  # yt-dlp --proxy http://127.0.0.1:1088 "$Url"
}

function dlog([string]$Name) {
  docker logs -fn 200 "$Name"
}

function dbash([string]$Name) {
  docker exec -it "$Name" /bin/bash;
}

function gk([string]$Name) {
  git checkout "$Name"
}

function csync([string]$Source, [string]$Dest) { 
  rclone sync -i "$Sourc" "$Dest"
}

$ansiblePath = 'G:\OneDrive\scripts\ansible'
function ap([string]$playbook, [string]$options, [string]$vars1, [string]$vars2) { 
  ansible-playbook -i $ansiblePath/inventory.yaml $ansiblePath/$playbook.yaml $options $vars1 $vars2
}

function tk() { ssh tk }
function nj() { ssh nj }
function sg() { ssh sg }
function y0() { ssh y0 }
function y1() { ssh y1; }
function y2() { ssh y2; }
function y3() { ssh y3; }
function y4() { ssh y4; }
function y5() { ssh y5; }
function y6() { ssh y6; }
function m1() { ssh m1; }
function m2() { ssh m2; }
function m3() { ssh m3; }
function j1() { ssh j1 }
function j2() { ssh j2; }
function j3() { ssh j3; }
function l1() { ssh l1; }
function l2() { ssh l2; }
function p1() { ssh p1; }
function p2() { ssh p2; }
function p3() { ssh p3; }
function p4() { ssh p4; }
function p75() { ssh p75; }
function p5() { ssh p5; }
function p6() { ssh p6; }
function p7() { ssh p7; }
function p8() { ssh p8; }
function p11() { p11; }
function p12() { p12; }
function p13() { p13; }
function p14() { p14; }
function p15() { p15; }
function p20() { p20; }
function p21() { p21; }
function p22() { p22; }
function p23() { p23; }
function p30() { p30; }

# https://github.com/starship/starship
Invoke-Expression (&starship init powershell)