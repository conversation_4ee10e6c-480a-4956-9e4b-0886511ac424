# PATH="$PATH:~/.local/bin"

alias ll='ls -la --block-size=M'

alias tn='tmux new -s'
alias ta='tmux attach -t'
alias tl='tmux ls'
alias td='tmux detach'
alias tk='tmux kill-session -t'

alias dps='docker ps'
alias dlog='docker logs -fn 200'
alias dbash='dbash(){ docker exec -it $1 /bin/bash;};dbash'
alias dash='dash(){ docker exec -it $1 /bin/sh;};dash' # https://stackoverflow.com/a/44804509/346701

alias klog='kubectl logs -f --tail=200'
alias pods='kubectl get pods'
alias kdd='kubectl delete deployment'
alias kdp='kubectl delete pod'
alias kap='kubectl apply -f'

alias proxyon="export http_proxy='http://127.0.0.1:1087';export https_proxy='http://127.0.0.1:1087'"
alias proxyoff="unset http_proxy;unset https_proxy"

alias vdl="yt-dlp --proxy socks5://127.0.0.1:1088"
alias csync="rclone sync -i --exclude '.DS_Store'"

# ssh-copy-id <EMAIL>
alias scp_sgp="scp_sgp(){ scp $1 ubuntu@**************:~;};scp_sgp"

# ssh-copy-id -o ProxyCommand="ssh -p 38748 <EMAIL> -W *************:22" czmp@*************:~
# alias ssh_p1="ssh -o ProxyCommand='ssh -p 38748 <EMAIL> -W *************:22' czmp@*************:~"
# alias scp_p1="scp_p1(){ scp -P 20022 $1 czmp@*************:~;};scp_p1"
# alias scp_p1j="scp_p1p(){ scp -o ProxyCommand='ssh -p 38748 <EMAIL> -W *************:22' $1 czmp@*************:~;};scp_p1p"

ansiblePath="/mnt/g/OneDrive/scripts/ansible"
ap() {
    ansible-playbook -i $ansiblePath/inventory.yaml $ansiblePath/$1.yaml $2 $3
}

if [ -d "$HOME/.dotnet" ] ; then
    PATH="$HOME/.dotnet:$PATH"
fi