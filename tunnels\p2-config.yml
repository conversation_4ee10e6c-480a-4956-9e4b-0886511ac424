tunnel: 1b41c347-68c7-4336-853c-355b1985a0fa
credentials-file: /home/<USER>/.cloudflared/1b41c347-68c7-4336-853c-355b1985a0fa.json
protocol: auto
ingress:
  - hostname: p2-ssh.socode.top
    service: ssh://localhost:22
  - hostname: p2-api.socode.top
    service: http://localhost:80
  - hostname: p2-hub.socode.top
    service: http://localhost:9133
  - hostname: p2-kibana.socode.top
    service: http://localhost:5601
  - hostname: p2-kafka-86.socode.top
    service: http://localhost:8086
  - hostname: p2-kafka-87.socode.top
    service: http://localhost:8087
  - hostname: p2-pg.socode.top
    service: tcp://localhost:5432
  - hostname: p2-clickhouse.socode.top
    service: http://localhost:8123
  - hostname: p2-minio.socode.top
    service: http://localhost:9000
  - hostname: p2-minio-console.socode.top
    service: http://localhost:9001
  - hostname: p2-grafana.socode.top
    service: http://localhost:3080
  - hostname: p1-prometheus.socode.top
    service: http://localhost:9090
  - hostname: p2-web.socode.top
    service: http://localhost:16002
  - service: http_status:404
