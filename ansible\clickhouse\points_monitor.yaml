# ap clickhouse/points_monitor --extra-vars="host=points_clickhouse"

- name: select create_time
  hosts: "{{ host }}"
  gather_facts: no
  tasks:
    - shell: >
        clickhouse-client --password CZMP.2021 --port 9030 -nq 
        "select create_time, check_time from cityserver_mater_pg_point.bs_dd_check_weight order by create_time desc limit 1;"
      register: show
    - name: std
      debug: msg="{{ show.stderr }} {{ show.stdout }}"
