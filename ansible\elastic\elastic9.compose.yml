# sudo vi /etc/sysctl.conf
# vm.max_map_count=262144
# sudo sysctl -p

volumes:
  esplugin:
    driver: local

networks:
  elastic:

services:
  elastic:
    image: "elasticsearch:${ELK_VERSION:-9.1.2}" # docker.elastic.co/* 没有国内加速
    container_name: elastic
    restart: on-failure
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      # for v9 single node mode, do not set any cluster envs!
      # node.name: elastic
      # cluster.name: "docker-cluster"
      # cluster.max_shards_per_node: 5000
      # ${INITIAL_MASTER_NODES_ENVKEY:-/dev/null}: ${INITIAL_MASTER_NODES:-/dev/null}
      # discovery.seed_hosts: ${SEED_HOSTS:-/dev/null}

      xpack.license.self_generated.type: basic
      xpack.monitoring.collection.enabled: "false"
    volumes:
      - type: bind
        source: $ES_DATA
        target: /usr/share/elasticsearch/data
      - type: volume
        source: esplugin
        target: /usr/share/elasticsearch/plugins
    networks:
      - elastic
    deploy:
      resources:
        limits:
          memory: 6g

  kibana:
    image: "kibana:${ELK_VERSION:-9.1.2}"
    container_name: kibana
    restart: on-failure
    depends_on:
      - elastic
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - "${KIBANA_PORT:-5601}:5601"
    environment:
      # https://www.elastic.co/guide/en/kibana/current/docker.html#environment-variable-config
      SERVER_NAME: kibana
      I18N_LOCALE: "zh-CN"
      SERVER_PUBLICBASEURL: "http://$NETWORK_PUBLISH_HOST:${KIBANA_PORT:-5601}"
    networks:
      - elastic

# https://www.elastic.co/docs/deploy-manage/deploy/self-managed/install-elasticsearch-docker-basic
# docker exec -it elastic /usr/share/elasticsearch/bin/elasticsearch-reset-password -u elastic
# docker cp elastic:/usr/share/elasticsearch/config/certs/http_ca.crt .
# curl --cacert http_ca.crt -u elastic:your_password https://localhost:9200/_cat/nodes

# only valid for 30 minutes
# docker exec -it elastic /usr/share/elasticsearch/bin/elasticsearch-create-enrollment-token -s kibana
