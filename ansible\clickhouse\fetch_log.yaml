# ap clickhouse/fetch_log -t perm -K -e="host=city_clickhouse"
# ap clickhouse/fetch_log -t perm -K -e="host=jintan_clickhouse"
# ap clickhouse/fetch_log -t perm -K -e="host=liyang_clickhouse"
# ap clickhouse/fetch_log -t perm -K -e="host=wujin"
# ap clickhouse/fetch_log -t perm -K -e="host=xinbei"
# ap clickhouse/fetch_log -t perm -K -e="host=points_jintan,points_wujin"
# ap clickhouse/fetch_log -t perm -K -e="host=points_xinbei"

# ap clickhouse/fetch_log -t fetch -e="host=city_clickhouse"
# ap clickhouse/fetch_log -t fetch -e="host=jintan_clickhouse"
# ap clickhouse/fetch_log -t fetch -e="host=liyang_clickhouse"
# ap clickhouse/fetch_log -t fetch -e="host=wujin"
# ap clickhouse/fetch_log -t fetch -e="host=xinbei"
# ap clickhouse/fetch_log -t fetch -e="host=points_jintan,points_wujin"
# ap clickhouse/fetch_log -t fetch -e="host=points_xinbei"

# ap clickhouse/fetch_log -t fetch -e="host=city_clickhouse,jintan_clickhouse,liyang_clickhouse,wujin,xinbei,points_czmp"

- name: Fetch Log
  hosts: "{{ host }}"
  tags: [fetch]

  tasks:
    - set_fact:
        log_path: /var/log/clickhouse-server/clickhouse-server.log
        err_path: /var/log/clickhouse-server/clickhouse-server.err.log

    - name: Tail content
      shell: tail -n 500 {{ log_path }} > /tmp/ch_log.log # become:yes instead cannot create /tmp/ch_log.log

    - ansible.builtin.fetch:
        src: /tmp/ch_log.log
        dest: ~/work/fetch_data/ch_logs/{{ inventory_hostname }}.log
        flat: true

    - name: Tail error
      shell: tail -n 1000 {{ err_path }} > /tmp/ch_error.log || true

    - ansible.builtin.fetch:
        src: /tmp/ch_error.log
        dest: ~/work/fetch_data/ch_errors/{{ inventory_hostname }}.log
        flat: true


- name: permissions
  hosts: "{{ host }}"
  tags: [perm]
  become: yes
  tasks:
    - set_fact:
        log_path: /var/log/clickhouse-server/clickhouse-server.log
        err_path: /var/log/clickhouse-server/clickhouse-server.err.log

    - name: log folder permissions
      file:
        path: /var/log/clickhouse-server
        mode: '0755'
        state: directory

    - name: log_path permissions
      file:
        path: '{{ log_path }}'
        mode: '0755'
    
    - name: err_path permissions
      file:
        path: '{{ err_path }}'
        mode: '0755'