version: "3.8"

services:
  minio:
    image: minio/minio:${MINIO_VERSION:-latest}
    container_name: minio
    restart: always
    command: server --console-address ":${CONSOLE_PORT:-9001}" --address ":${PORT}" --certs-dir "/certs" /data
    network_mode: host
    environment:
      MINIO_ROOT_USER: $MINIO_ROOT_USER
      MINIO_ROOT_PASSWORD: $MINIO_ROOT_PASSWORD
      # MINIO_BROWSER_REDIRECT_URL: $MINIO_BROWSER_REDIRECT_URL
      MINIO_PROMETHEUS_AUTH_TYPE: public
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT}/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    volumes:
      - type: bind
        source: "${MINIO_DATA}"
        target: /data
      - type: bind
        source: "${CERTS_DIR:-~/.minio/certs}"
        target: /certs
