# ap database/entry -t city --extra-vars="play=cdc_flush"

- name: cdc_flush
  hosts: "{{ inventory }}"
  gather_facts: no
  tasks:
    - postgresql_query:
        login_host: 127.0.0.1
        db: "{{ db }}"
        login_password: "{{ login_password }}"
        port: "{{ pg_port | default(5432) | int }}"
        autocommit: yes
        query: >
          SELECT pg_terminate_backend(active_pid) FROM pg_replication_slots WHERE slot_name like 'flow_%';
          SELECT pg_drop_replication_slot(slot_name) FROM pg_replication_slots WHERE slot_name like 'flow_%';
        # query: >
        #   SELECT pg_terminate_backend(active_pid) FROM pg_replication_slots WHERE slot_name like 'roads_slot_%';
        #   SELECT pg_drop_replication_slot(slot_name) FROM pg_replication_slots WHERE slot_name like 'roads_slot_%'; 
      register: show
    - name: std
      debug: msg="{{ show.statusmessage }} {{ show.query_result }}"
