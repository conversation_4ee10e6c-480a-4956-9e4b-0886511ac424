version: "3.3"

services:
  logstash:
    image: "logstash:${ELK_VERSION:-7.15.2}"
    container_name: logstash
    restart: on-failure
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - "5044:5044"
      - "5000:5000/tcp"
      - "5000:5000/udp"
      - "9600:9600"
    environment:
      LS_JAVA_OPTS: $LS_JAVA_OPTS
      ELASTIC_HOSTS: "http://${ELASTIC_HOSTS:-host.docker.internal:9200}"
      ELASTIC_USERNAME: elastic
      ELASTIC_PASSWORD: $ELASTIC_PASSWORD
    volumes:
      - type: bind
        source: ./pipeline.conf
        target: /usr/share/logstash/pipeline/logstash.conf
        read_only: true