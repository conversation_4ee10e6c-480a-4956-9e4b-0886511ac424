const regions = [
  "香港",
  "韩国",
  "日本",
  "台湾",
  "越南",
  "泰国",
  "马来西亚",
  "新加坡",
  "印度尼西亚",
  "悉尼",
  "洛杉矶",
  "旧金山",
  "西雅图",
  "温哥华",
  "以色列",
];

const delay = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

const waitElement = (selector, timeout) => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    const checkExist = setInterval(() => {
      const element = document.querySelector(selector);
      if (element) {
        clearInterval(checkExist);
        resolve(element);
      } else if (Date.now() - startTime >= timeout) {
        clearInterval(checkExist);
        reject(new Error(`"${selector}" not found within ${timeout}ms`));
      }
    }, 200);
  });
};

// support cors: https://github.com/msoap/shell2http/issues/104
var ping = async (ip) => {
  try {
    const response = await fetch("http://127.0.0.1:8080/ping?ip=" + ip);
    const text = await new Response(response.body).text();
    console.log(`${ip} reachable ${response.ok} text:${text}`, response);
    if (text.includes("timeout")) return false;
    return true;
  } catch (error) {
    console.log(`${ip} fail`, error);
  }
  return false;
};

const container = document.querySelector("[data-test='content-drawer']");
let content = container.querySelector("[data-test='locations-tab-content']");

var processOneRegion = async (region) => {
  console.log("start region", region);
  const items = content.querySelectorAll("[data-test='menu-list-item']");
  const targetItem = Array.from(items).find((el) =>
    el.textContent.includes(region)
  );
  if (targetItem == null) {
    console.error("targetItem null", region);
    return false;
  }
  targetItem.click();

  // await delay(3000);
  // const dialog = document.querySelector("[data-test='dialog-box']");
  let dialog;
  try {
    dialog = await waitElement("[data-test='dialog-box']", 10000);
  } catch (error) {
    console.error("dialog not found", region, error);
    return false;
  }

  const values = dialog.querySelectorAll("[data-test='info-list-item-value']");
  const ip = values[2].textContent;
  if (await ping(ip)) {
    console.warn("ping success", region, ip, values[3].textContent);
  }

  dialog.querySelector("[data-test='secondary-button']").click();
  return true;
};

(async () => {
  if (content == null) {
    document.querySelector("[data-test='tab-locations']").click();
    console.log("switch tab");
    await delay(1000);
    content = container.querySelector("[data-test='locations-tab-content']");
  }

  for (const region of regions) {
    const done = await processOneRegion(region);
    if (!done) break;
  }
})();
