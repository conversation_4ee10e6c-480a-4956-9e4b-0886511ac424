# ansible-galaxy collection install community.general
# ap user -K
# https://docs.ansible.com/ansible/latest/collections/community/general/sudoers_module.html

# sudo apt install members
# members admin

- name: add user
  hosts: liyang
  # hosts: liyang_old
  become: yes
  vars:
    password: "czmp.2024#sc"
  tasks:
    - name: securer
      ansible.builtin.user:
        name: securer
        comment: securer officer
        state: present
        group: admin
        password: "{{ password | password_hash('sha512')}}"

