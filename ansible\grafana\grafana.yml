version: "3.6"

services:
  grafana:
    container_name: grafana_data
    image: "grafana/grafana-oss:${GRAFANA_VERSION:-latest}"
    restart: always
    user: "$UID:$GID"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    volumes:
      - ${GRAFANA_DATA}:/var/lib/grafana
      - ./grafana.ini:/etc/grafana/grafana.ini
      # - ./grafana_icon.svg:/usr/share/grafana/public/img/grafana_icon.svg
    # environment:
    #   GF_INSTALL_PLUGINS: grafana-clickhouse-datasource,vertamedia-clickhouse-datasource,grafana-clock-panel,grafana-simple-json-datasource
    #   GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS: vertamedia-clickhouse-datasource # for https://grafana.com/grafana/dashboards/13606-clickhouse-performance-monitor-xm-uat

# https://grafana.com/docs/grafana/latest/installation/docker
# default account: admin:admin

# https://grafana.com/docs/grafana/latest/administration/configure-docker
# docker cp grafana_data:/etc/grafana/grafana.ini grafana11.ini

# docker exec -it grafana /bin/bash
# grafana-cli plugins install vertamedia-clickhouse-datasource
# https://grafana.com/grafana/plugins/vertamedia-clickhouse-datasource/?tab=installation

# config: root_url = http://http://61.132.100.83:14003/

# Customizing login screen and logo must be without docker:
# https://isaqueprofeta.medium.com/customizing-grafana-login-screen-and-logo-for-your-brand-c90d2dba0141
