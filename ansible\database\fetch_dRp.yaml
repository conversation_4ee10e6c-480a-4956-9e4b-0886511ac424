# ap database/entry -t city --extra-vars="play=fetch_dRp"
# ap database/entry -t area --extra-vars="play=fetch_dRp"
# ap database/entry -t points --extra-vars="play=fetch_dRp"

# ap database/entry -t city,area,points --extra-vars="play=fetch_dRp"

- name: Fetch dRp
  hosts: "{{ inventory }}"
  gather_facts: no
  tasks:
    - postgresql_query:
        login_host: 127.0.0.1
        db: "{{ db }}"
        login_password: "{{ login_password }}"
        port: "{{ pg_port | default(5432) | int }}"
        autocommit: yes
        query: >
          SELECT pubname AS "Name",
            pg_catalog.pg_get_userbyid(pubowner) AS "Owner",
            puballtables AS "All tables",
            pubinsert AS "Inserts",
            pubupdate AS "Updates",
            pubdelete AS "Deletes",
            pubtruncate AS "Truncates",
            pubviaroot AS "Via root"
          FROM pg_catalog.pg_publication
          ORDER BY 1;
      register: result

    - name: Save result
      copy:
        content: "{{ result.query_result | to_nice_json }}"
        dest: /tmp/pg_dRp.json

    - fetch:
        src: /tmp/pg_dRp.json
        dest: ~/work/fetch_data/pg_dRp/{{ inventory_hostname }}.json
        flat: true