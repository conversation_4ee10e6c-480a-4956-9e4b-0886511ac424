- name: standby
  hosts: "{{ inventory }}"
  gather_facts: no
  tasks:
    - name: CREATE INDEX
      postgresql_query:
        login_host: 127.0.0.1
        db: "{{ db }}"
        login_password: "{{ login_password }}"
        port: "{{ pg_port | default(5432) | int }}"
        query: |
          CREATE INDEX check_weight_vehicleno_index ON public.bs_dd_check_weight USING hash (vehicle_no);
          CREATE INDEX check_weight_siteid_index ON public.bs_dd_check_weight USING btree (site_id);
          CREATE INDEX check_weight_companyid_index ON public.bs_dd_check_weight USING btree (company_id);
          CREATE INDEX check_weight_checktime_index ON public.bs_dd_check_weight USING btree (check_time);
    - name: ALTER TYPE
      postgresql_query:
        login_host: 127.0.0.1
        db: "{{ db }}"
        login_password: "{{ login_password }}"
        port: "{{ pg_port | default(5432) | int }}"
        query: |
          ALTER TABLE alerting ALTER COLUMN check_weight_id TYPE CHARACTER VARYING(64);
          ALTER TABLE sms_log ALTER COLUMN check_weight_id TYPE CHARACTER VARYING(64);
          ALTER TABLE bs_dd_vehicle_info ALTER COLUMN check_weight_id TYPE CHARACTER VARYING(64);

          ALTER TABLE bs_dd_check_weight DROP COLUMN id;
          ALTER TABLE bs_dd_check_weight DROP COLUMN bs_dd_check_weight_id;
          ALTER TABLE bs_dd_check_weight ADD CONSTRAINT bs_dd_check_weight_id_pk PRIMARY KEY (check_no);

          ALTER TABLE bs_dd_vehicle_info DROP COLUMN id;
          ALTER TABLE bs_dd_vehicle_info ADD CONSTRAINT bs_dd_vehicle_info_pk PRIMARY KEY (vehicle_no);
          ALTER TABLE bs_dd_vehicle_info DROP CONSTRAINT ux_bs_dd_vehicle_info__vehicle_no;

          ALTER TABLE large_transport DROP COLUMN id;
          ALTER TABLE large_transport ADD CONSTRAINT large_transport_pk PRIMARY KEY (transport_license_id);
          ALTER TABLE large_transport DROP CONSTRAINT ux_large_transport__transport_license_id;

          ALTER TABLE bs_sys_ytqy_lane ALTER COLUMN camera_index_code TYPE TEXT;
    - name: SEQUENCE
      postgresql_query:
        login_host: 127.0.0.1
        db: "{{ db }}"
        login_password: "{{ login_password }}"
        port: "{{ pg_port | default(5432) | int }}"
        query: |
          ALTER TABLE operation_log ALTER COLUMN id SET DEFAULT nextval('operationlog_sequence_generator'::regclass);
          ALTER TABLE black_list ALTER COLUMN id SET DEFAULT nextval('blacklist_sequence_generator'::regclass);
          ALTER TABLE alerting ALTER COLUMN id SET DEFAULT nextval('alerting_sequence_generator'::regclass);

          CREATE SEQUENCE public.overvehicle_statistic_sequence_generator INCREMENT 1 START WITH 1 CACHE 1;
          ALTER TABLE over_vehicle_statistic ALTER COLUMN id SET DEFAULT nextval('overvehicle_statistic_sequence_generator'::regclass);
          CREATE SEQUENCE public.danger_goods_vehicle_log_sequence_generator INCREMENT 1 START WITH 1 CACHE 1;
          ALTER TABLE danger_goods_vehicle_log ALTER COLUMN id SET DEFAULT nextval('danger_goods_vehicle_log_sequence_generator'::regclass);