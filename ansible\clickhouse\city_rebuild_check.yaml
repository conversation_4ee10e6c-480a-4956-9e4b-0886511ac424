# ap clickhouse/city_rebuild_check

# - name: restart clickhouse
#   hosts: y6_root
#   tasks:
#     - name: restart clickhouse
#       shell: "service clickhouse-server restart"
#       run_once: true

# - name: drop postgres slot
#   hosts: y4_root
#   tasks:
#     - name: DROP PUBLICATION
#       postgresql_query:
#         login_host: 127.0.0.1
#         login_password: CZMP^2023
#         db: czmpcity
#         query: DROP PUBLICATION IF EXISTS czmpcity_ch_publication
#     - name: kill COPY process
#       shell: |
#         ps aux | grep '\WCOPY$' | awk '{print $2}' | xargs sudo kill -9
#       ignore_errors: true
#     # - name: restart pg
#     #   shell: "systemctl restart postgresql"
#     #   run_once: true
#     # - name: Pause to wait for pg recovery
#     #   ansible.builtin.pause:
#     #     seconds: "5"
#     - name: drop_replication_slot
#       postgresql_query:
#         login_host: 127.0.0.1
#         login_password: CZMP^2023
#         db: czmpcity
#         query: SELECT pg_drop_replication_slot('czmpcity')
#       retries: 3
#       delay: 2
#       register: result
#       until: result is succeeded
#       ignore_errors: true

- name: rebuild clickhouse database
  hosts: y6_root
  gather_facts: no
  tasks:
    - name: DROP database
      shell: |
        clickhouse-client --password CZMP.2022 --port 9030 -nq "DROP DATABASE cityserver_mater_pg_check"
      ignore_errors: true
    - name: CREATE database
      shell: >
        clickhouse-client --password CZMP.2022 --port 9030 -nq "
        SET allow_experimental_database_materialized_postgresql=1;
        CREATE DATABASE cityserver_mater_pg_check
        ENGINE = MaterializedPostgreSQL('*************:5432', 'replication', 'czmpcity', 'CZMP^2023') 
        SETTINGS 
        materialized_postgresql_tables_list = 'bs_dd_check_weight', 
        materialized_postgresql_max_block_size=1;
        SHOW TABLES FROM cityserver_mater_pg_check;
        "
      register: show
    - name: std
      debug: msg="{{ show.stderr }} {{ show.stdout }}"
