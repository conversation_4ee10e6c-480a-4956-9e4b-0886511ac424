# https://docs.ansible.com/archive/ansible/2.3/docker_container_module.html

# ap docker --extra-vars="host=j1,l1,xb,wj name=cityserver_point"
# ap docker --extra-vars="host=points"
# ap docker --extra-vars="host=liyang"

- name: docker
  hosts: "{{ host }}"
  gather_facts: no
  tasks:
    # - name: absent
    #   community.docker.docker_container:
    #     name: "{{ name | default('flow_hub') }}"
    #     state: absent
    #     force_kill: yes
    #   ignore_errors: true

    - name: stopped
      community.docker.docker_container:
        name: "{{ name | default('flow_hub') }}"
        state: stopped
        force_kill: yes
      ignore_errors: true

    # - name: started
    #   community.docker.docker_container:
    #     name: "{{ name | default('flow_hub') }}"
    #     state: started
    #     restart: no
    #   ignore_errors: true

