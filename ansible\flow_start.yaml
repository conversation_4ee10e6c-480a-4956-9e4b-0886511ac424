# ap flow_start -t city -K

# - name: restart city hubs
#   hosts: media
#   gather_facts: no
#   tasks:
#     - name: restart flow_hub
#       community.docker.docker_container:
#         name: flow_hub
#         state: started
#         restart: yes
#       ignore_errors: true
#     - name: restart flow_verify
#       community.docker.docker_container:
#         name: flow_verify
#         state: started
#         restart: yes
#       ignore_errors: true

- name: start hik-ytqy
  hosts: y1
  become: yes
  tasks:
    - name: ytqy_deploy.sh
      script: ytqy_deploy.sh
      args:
        chdir: /home/<USER>/czmp-ytqy
