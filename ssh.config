# Read more about SSH config files: https://linux.die.net/man/5/ssh_config
# cp ssh.config ~/.ssh/config

# windows/macps replace ProxyCommand: 
# C:\\Program1\\cloudflared\\cloudflared access ssh --hostname %h
# /opt/homebrew/bin/cloudflared access ssh --hostname %h

# https://cms-sw.github.io/tutorial-proxy.html
# Host github.com
#     User git
#     ProxyCommand nc -X connect -x 127.0.0.1:7896 %h %p
# Host ssh.dev.azure.com
#     User git
#     ProxyCommand nc -X connect -x 127.0.0.1:7875 %h %p

# Host github.com
#     HostName github.com
#     User git
#     Port 1122
    
# Host codeup.aliyun.com
#     HostName codeup.aliyun.com
#     User git
#     Port 1122

Host zt
  HostName **************
  User root

Host zta
  HostName **************
  User azure

Host xiong
    # HostName **************
    # ring.link
    HostName **********
    User xiong

Host mn
    HostName *************
    User kuma
Host mnc
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName mini-ssh.socode.top
    User kuma
Host mnt
    HostName **************
    User kuma

# z5z5/root:Z565656z#
Host tk
    HostName ************
    User zic

Host tkp
    ProxyJump nj
    HostName ************
    User zic

# z5z5
Host nj
    HostName *************
    User zic

Host of
    HostName ************
    User czmp
Host ofc
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName of-ssh.windcss.com
    User czmp

Host y0
    HostName *************
    Port 1122
    User czmp
Host y0q
    ProxyJump <EMAIL>:15600
    HostName *************
    Port 1122
    User czmp
Host y0t
    HostName **********
    # HostName *************
    Port 1122
    User czmp
Host y0c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName y0-ssh.windcss.com
    User czmp

Host y1
    HostName *************
    Port 1122
    User czmp
Host y1q
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName y1-ssh.windcss.com
    User czmp
Host y1t
    HostName **********
    Port 1122
    User czmp
Host y1q
    ProxyJump <EMAIL>:15600
    HostName *************
    Port 1122
    User czmp

Host y2
    HostName *************
    Port 1122
    User czmp
Host y2q
    ProxyJump <EMAIL>:15600
    HostName *************
    Port 1122
    User czmp
Host y2c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName y2-ssh.windcss.com
    User czmp
Host y2t
    HostName **********0
    Port 1122
    User czmp

# ssh-copy-id -o ProxyCommand="ssh -p 33396 <EMAIL> -W *************:1122" czmp@*************
Host y3
    HostName *************
    Port 1122
    User czmp
Host y3q
    ProxyJump <EMAIL>:15600
    HostName *************
    Port 1122
    User czmp
Host y3c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName y3-ssh.windcss.com
    User czmp

Host y4
    HostName *************
    Port 1122
    User czmp
Host y4q
    ProxyJump <EMAIL>:15600
    HostName *************
    Port 1122
    User czmp
Host y4c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName y4-ssh.windcss.com
    User czmp
Host y4t
    HostName **********
    # HostName ************
    Port 1122
    User czmp

Host y5
    HostName *************
    Port 1122
    User czmp
Host y5q
    ProxyJump <EMAIL>:15600
    HostName *************
    Port 1122
    User czmp
Host y5c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName y5-ssh.windcss.com
    User czmp

Host y6
    HostName *************
    Port 1122
    User czmp
Host y6q
    ProxyJump <EMAIL>:15600
    HostName *************
    Port 1122
    User czmp
Host y6_root
    ProxyJump <EMAIL>:15600
    HostName *************
    Port 1122
    User root
Host y6c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName y6-ssh.windcss.com
    User czmp

Host m1
    HostName *************
    Port 1122
    User czmp
Host m1q
    ProxyJump <EMAIL>:15600
    HostName *************
    Port 1122
    User czmp
Host m1c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName m1-ssh.windcss.com
    User czmp
Host m1t
    HostName **********
    # HostName ************
    Port 1122
    User czmp

Host m2
    HostName *************
    Port 1122
    User czmp
Host m2q
    ProxyJump <EMAIL>:15600
    HostName *************
    Port 1122
    User czmp
Host m2c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName m2-ssh.windcss.com
    User czmp

Host m3
    HostName *************
    Port 1122
    User czmp
Host m3q
    ProxyJump <EMAIL>:15600
    HostName *************
    Port 1122
    User czmp
Host m3c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName m3-ssh.windcss.com
    User czmp

# Czmp.2021 root/admin@224488 azure/CZMP.2021
Host xb
    HostName *************
    User czmp
Host xbq
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp

# Czmp.2021
Host wj
    HostName *************
    User czmp
Host wjq
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp

Host j1
    HostName *************
    User czmp
Host j1q
    HostName dgvedo17.beesnat.com
    Port 15600
    User czmp
Host j1c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName jt1-ssh.windcss.com
    User czmp

Host j2
    HostName *************
    User czmp
Host j2q
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp
Host j2c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName jt2-ssh.windcss.com
    User czmp
Host j2t
    HostName **********
    User czmp

Host j3
    HostName *************
    User czmp
Host j3q
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp
Host j3c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName jt3-ssh.windcss.com
    User czmp


Host lo1
    HostName *************
    Port 1122
    User administrator
Host lo1c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName y0-ly1-ssh.windcss.com
    User administrator

Host lo2
    HostName *************
    Port 1122
    User serv
Host lo2c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName y0-ly2-ssh.windcss.com
    User serv

Host l1
    HostName ************
    Port 1122
    User czmp
Host l1r
    HostName **********
    Port 1122
    User czmp
Host l2
    HostName ************
    Port 1122
    User czmp
Host l3
    HostName ************
    Port 1122
    User czmp
Host l4
    HostName ************
    Port 1122
    User czmp

Host p1
    HostName ***********12
    User czmp
Host p1q
    ProxyJump <EMAIL>:15600
    HostName ***********12
    User czmp
Host p1c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p1-ssh.windcss.com
    User czmp

Host p2
    HostName ***********39
    User czmp
Host p2q
    ProxyJump <EMAIL>:15600
    HostName ***********39
    User czmp
Host p2c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p2-ssh.windcss.com
    User czmp

Host p3
    HostName *************
    User czmp
Host p3q
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp
Host p3c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p3-ssh.windcss.com
    User czmp

Host p4
    HostName *************
    User czmp
Host p4q
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp
Host p4c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p4-ssh.windcss.com
    User czmp

Host p75
    HostName *************
    User czmp
Host p75q
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp
Host p75c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p75-ssh.windcss.com
    User czmp

Host p5
    HostName *************
    User czmp
Host p5q
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp
Host p5c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p5-ssh.windcss.com
    User czmp

Host p6
    HostName *************
    User czmp
Host p6q
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp
Host p6c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p6-ssh.windcss.com
    User czmp

Host p7
    HostName *************
    User czmp
Host p7q
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp
Host p7c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p7-ssh.windcss.com
    User czmp

Host p8
    HostName *************
    User czmp
Host p8q
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp
Host p8c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p8-ssh.windcss.com
    User czmp

Host p11
    HostName *************
    User xbzc
Host p11q
    ProxyJump <EMAIL>:15600
    HostName *************
    User xbzc
Host p11c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p11-ssh.windcss.com
    User xbzc

Host p12
    HostName *************
    User xbzc
Host p12q
    ProxyJump <EMAIL>:15600
    HostName *************
    User xbzc
Host p12c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p12-ssh.windcss.com
    User xbzc

Host p13
    HostName *************
    User xbzc
Host p13q
    ProxyJump <EMAIL>:15600
    HostName *************
    User xbzc
Host p13c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p13-ssh.windcss.com
    User xbzc

Host p14
    HostName *************
    User xbzc
Host p14q
    ProxyJump <EMAIL>:15600
    HostName *************
    User xbzc
Host p14c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p14-ssh.windcss.com
    User xbzc

Host p15
    HostName *************
    User xbzc
Host p15q
    ProxyJump <EMAIL>:15600
    HostName *************
    User xbzc
Host p15c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p15-ssh.windcss.com
    User xbzc

Host p21
    HostName *************
    User czmp
Host p21q
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp
Host p21t
    HostName ***********
    User czmp
Host p21c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p21-ssh.windcss.com
    User czmp

Host p22
    HostName *************
    User czmp

Host p22q
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp
Host p22o
    HostName *************
    User czmp

Host p23
    HostName *************
    User czmp
Host p23q
    ProxyJump <EMAIL>:15600
    HostName *************
    User czmp
Host p23c
    ProxyCommand /opt/homebrew/bin/cloudflared access ssh --hostname %h
    HostName p23-ssh.windcss.com
    User czmp
Host p23t
    HostName *************
    User czmp


# Station.2022+
Host p20
    HostName *************
    Port 1122
    User czmp

Host p26
    HostName *************
    User czmp
# Admin@224488
Host p26s
    HostName *************
    Port 1122
    User root

Host p27
    HostName *************
    User czmp

# Station.2021
Host z148
    HostName *************
    Port 1122
    User administrator
Host z148q
    ProxyJump <EMAIL>:15600
    HostName *************
    Port 1122
    User administrator

# Admin@224488
Host z2
    HostName ***********
    User root
Host z2q
    ProxyJump <EMAIL>:15600
    HostName ***********
    User root

Host p40
    # Station.2022+ link p20
    HostName *************
    Port 1122
    User czmp

Host p41
    HostName *************
    User czmp

# **************
Host p30
    # HostName **************
    # headscale
    # HostName **********
    # ring.link
    HostName **********
    User czmp
# *************
Host p31
    # HostName ***************
    # ring.link
    HostName **********
    User czmp
# ************
Host p32
    # tailscale
    HostName ************
    User czmp

# eI!PRGi]62PGOOf
Host fm
    HostName **************
    Port 50022
    User czmp
Host fmc
    HostName **************
    Port 50022
    User code
Host fmh
    HostName **************
    Port 50022
    User hkisup

# CZMP.2021
Host ep1
    HostName *************
    Port 1122
    User czmp

Host ep2
    HostName *************
    Port 1123
    User czmp
