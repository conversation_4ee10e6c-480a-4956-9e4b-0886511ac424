- name: dicts
  hosts: "{{ inventory }}"
  gather_facts: no
  tasks:
    - name: INSERT/UPDATE DICTS
      postgresql_query:
        login_host: 127.0.0.1
        db: "{{ db }}"
        login_password: "{{ login_password }}"
        port: "{{ pg_port | default(5432) | int }}"
        query: |
          UPDATE bs_sys_dict set dict_name = '{{ item.name }}', dict_value = '{{ item.value }}', status = '1' WHERE dict_code = '{{ item.key }}';
          INSERT INTO bs_sys_dict(id, dict_code, dict_type, dict_name, status, dict_value)
          SELECT nextval('sequence_generator'), '{{ item.key }}', 'config', '{{ item.name }}', 1, '{{ item.value }}'
          WHERE NOT EXISTS (
            SELECT dict_code FROM bs_sys_dict WHERE dict_code = '{{ item.key }}'
          );
      loop:
        # - { key: 'ALERTING_OVER_WEIGHT_ENABLE', name: '超限运输 启用', value: 'false' }
        # - { key: 'ALERTING_OVER_WEIGHT_VOICE', name: '超限运输 声音', value: 'true' }
        # - { key: 'ALERTING_OVER_WEIGHT_MINIMUM', name: '超限运输 最小', value: '20' }
        # - { key: 'ALERTING_OVER_WEIGHT_MAXIMUM', name: '超限运输 最大', value: '100' }
          
        # - { key: 'ALERTING_DOUBLE100_ENABLE', name: '双百超限 启用', value: 'false' }
        # - { key: 'ALERTING_DOUBLE100_VOICE', name: '双百超限 声音', value: 'true' }
        # - { key: 'ALERTING_DOUBLE100_NONO_ENABLE', name: '双百超限 无车牌 启用', value: 'true' }
        # - { key: 'ALERTING_DOUBLE100_NONO_VOICE', name: '双百超限 无车牌 声音', value: 'false' }
        # - { key: 'ALERTING_DOUBLE100_OVER_RATE', name: '双百超限 最小超限率', value: '100' }
        # - { key: 'ALERTING_DOUBLE100_TOTAL_WEIGHT', name: '双百超限 最小总重', value: '100' }

        # - { key: 'ALERTING_BLACK_LIST_ENABLE', name: '黑名单 启用', value: 'false' }
        # - { key: 'ALERTING_BLACK_LIST_VOICE', name: '黑名单 声音', value: 'true' }

        # - { key: 'ALERTING_EXTERNAL_LIST_ENABLE', name: '外部黑名单 启用', value: 'false' }
        # - { key: 'ALERTING_EXTERNAL_LIST_VOICE', name: '外部黑名单 声音', value: 'true' }
          
        # - { key: 'ALERTING_ILLEGAL_ENABLE', name: '违章 启用', value: 'false' }
        # - { key: 'ALERTING_ILLEGAL_VOICE', name: '违章 声音', value: 'true' }
          
        # - { key: 'ALERTING_UNUSUAL_ENABLE', name: '数据异常 启用', value: 'false' }
        # - { key: 'ALERTING_UNUSUAL_VOICE', name: '超限运输 声音', value: 'true' }

        # - { key: 'ALERTING_LARGETRANSPORT_ENABLE', name: '大件运输 启用', value: 'false' }
        # - { key: 'ALERTING_LARGETRANSPORT_VOICE', name: '超限运输 声音', value: 'true' }

        # - { key: 'ALERTING_COMPANY_ENABLE', name: '货运源头超限 启用', value: 'false' }
        # - { key: 'ALERTING_COMPANY_VOICE', name: '超限运输 声音', value: 'true' }
        # - { key: 'ALERTING_COMPANY_RATE', name: '货运源头超限 最小超限率', value: '20' }
          
        # - { key: 'ALERTING_COMPANY_DOUBLE100_ENABLE', name: '货运源头双百超限 启用', value: 'true' }
        # - { key: 'ALERTING_COMPANY_DOUBLE100_VOICE', name: '货运源头双百超限 声音', value: 'false' }
        # - { key: 'ALERTING_COMPANY_DOUBLE100_OVER_RATE', name: '货运源头双百超限 最小超限率', value: '100' }
        # - { key: 'ALERTING_COMPANY_DOUBLE100_TOTAL_WEIGHT', name: '货运源头双百超限 最小总重', value: '100' }

        # - { key: 'ALERTING_FLOW_ENABLE', name: '无过车预警 启用', value: 'false' }
        # - { key: 'ALERTING_FLOW_VOICE', name: '超限运输 声音', value: 'true' }
        # - { key: 'ALERTING_FLOW_INTERVAL', name: '无过车预警 间隔', value: '30' }

        # - { key: 'ALERTING_DISK_ENABLE', name: '磁盘容量 启用', value: 'false' }
        # - { key: 'ALERTING_DISK_VOICE', name: '超限运输 声音', value: 'true' }
        # - { key: 'ALERTING_DISK_RAFT', name: '磁盘容量 使用率', value: '90' }
        # - { key: 'ALERTING_DISK_INTERVAL', name: '磁盘容量 间隔', value: '30' }
          
        # - { key: 'ALERTING_DEVICE_OFFLINE_ENABLE', name: '设备离线 启用', value: 'false' }
        # - { key: 'ALERTING_DEVICE_OFFLINE_VOICE', name: '超限运输 声音', value: 'true' }
        # - { key: 'ALERTING_DEVICE_OFFLINE_INTERVAL', name: '设备离线 间隔', value: '30' }
          
        # - { key: 'ALERTING_TEMPERATURE_ENABLE', name: '温度 启用', value: 'false' }
        # - { key: 'ALERTING_TEMPERATURE_VOICE', name: '超限运输 声音', value: 'true' }
        # - { key: 'ALERTING_TEMPERATURE_HIGHT_RAFT', name: '温度 上限', value: '80' }
        # - { key: 'ALERTING_TEMPERATURE_LOW_RAFT', name: '温度 下限', value: '10' }
        # - { key: 'ALERTING_TEMPERATURE_INTERVAL', name: '温度 间隔', value: '30' }
          
        # - { key: 'ALERTING_HUMIDITY_ENABLE', name: '湿度 启用', value: 'false' }
        # - { key: 'ALERTING_HUMIDITY_VOICE', name: '超限运输 声音', value: 'true' }
        # - { key: 'ALERTING_HUMIDITY_HIGHT_RAFT', name: '湿度 上限', value: '40' }
        # - { key: 'ALERTING_HUMIDITY_LOW_RAFT', name: '湿度 下限', value: '20' }
        # - { key: 'ALERTING_HUMIDITY_INTERVAL', name: '湿度 间隔', value: '30' }
          
        # - { key: 'ALERTING_SMOKE_ENABLE', name: '烟感 启用', value: 'false' }
        # - { key: 'ALERTING_SMOKE_VOICE', name: '超限运输 声音', value: 'true' }
        # - { key: 'ALERTING_SMOKE_INTERVAL', name: '烟感 间隔', value: '1' }
          
        # - { key: 'ALERTING_WATER_ENABLE', name: '水感 启用', value: 'false' }
        # - { key: 'ALERTING_WATER_VOICE', name: '超限运输 声音', value: 'true' }
        # - { key: 'ALERTING_WATER_INTERVAL', name: '水感 间隔', value: '1' }
          
        # - { key: 'ALERTING_DOOR_ENABLE', name: '门磁 启用', value: 'false' }
        # - { key: 'ALERTING_DOOR_VOICE', name: '超限运输 声音', value: 'true' }
        # - { key: 'ALERTING_DOOR_INTERVAL', name: '门磁 间隔', value: '1' }
          
        # - { key: 'ALERTING_CHECKPERIOD_ENABLE', name: '检定期 启用', value: 'false' }
        # - { key: 'ALERTING_CHECKPERIOD_VOICE', name: '超限运输 声音', value: 'true' }
        # - { key: 'ALERTING_CHECKPERIOD_RAFT', name: '检定期 阀值', value: '60' }
        # - { key: 'ALERTING_CHECKPERIOD_INTERVAL', name: '检定期 间隔', value: '1' }
          
        # - { key: 'ALERTING_FENCE_ENABLE', name: '围栏预警 启用', value: 'true' }
        # - { key: 'ALERTING_FENCE_VOICE', name: '围栏预警 声音', value: 'false' }
        # - { key: 'ALERTING_TERRITORY_ENABLE', name: '境内预警 启用', value: 'false' }
        # - { key: 'ALERTING_TERRITORY_VOICE', name: '境内预警 声音', value: 'true' }
          
        # - { key: 'ALERTING_SPECIAL_VEHICLE_TYPE_ENABLE', name: '专项车辆类型 启用', value: 'true' }
        # - { key: 'ALERTING_SPECIAL_VEHICLE_TYPE_VOICE', name: '专项车辆类型 声音', value: 'false' }
        
        # - { key: 'NOTIFI_SMS_START_HOUR', name: '超限短信通知起始时间', value: '' }
        # - { key: 'NOTIFI_SMS_END_HOUR', name: '超限短信通知结束时间', value: '' }
        # - { key: 'NOTIFI_SMS_OVER_RATE', name: '超限短信通知最小超限率', value: '' }
        # - { key: 'NOTIFI_VOICE_START_HOUR', name: '超限语音通知起始时间', value: '' }
        # - { key: 'NOTIFI_VOICE_END_HOUR', name: '超限语音通知结束时间', value: '' }
        # - { key: 'NOTIFI_VOICE_OVER_RATE', name: '超限语音通知最小超限率', value: '' }
        # - { key: 'NOTIFI_SMS_WEEK_DAY', name: '超限短信通知星期', value: '' }
        # - { key: 'NOTIFI_VOICE_WEEK_DAY', name: '超限语音通知星期', value: '' }
        
        # - { key: 'ALERTING_DANGER_GOODS_ENABLE', name: '危险品车辆 启用', value: 'true' }
        # - { key: 'ALERTING_DANGER_GOODS_VOICE', name: '危险品车辆 声音', value: 'false' }
          
        # - { key: 'ALERTING_SCALES_ENABLE', name: '称重设备 启用', value: 'true' }
        # - { key: 'ALERTING_SCALES_LANE_ENABLE', name: '车道称重设备 启用', value: 'true' }
        # - { key: 'ALERTING_SCALES_VOICE', name: '称重设备 声音', value: 'false' }
        # - { key: 'ALERTING_SCALES_INTERVAL', name: '称重设备 间隔', value: '30' }
          

        - { key: 'ScaleWeightPercent_2', name: '', value: '1' }
        - { key: 'ScaleWeightPercent_3', name: '', value: '1' }
        - { key: 'ScaleWeightPercent_4', name: '', value: '1' }
        - { key: 'ScaleWeightPercent_5', name: '', value: '1' }
        - { key: 'ScaleWeightPercent_6', name: '', value: '1' }