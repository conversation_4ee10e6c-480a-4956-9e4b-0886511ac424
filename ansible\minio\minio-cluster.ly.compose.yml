version: "3.7"

x-minio-common: &minio-common
  image: minio/minio:${MINIO_VERSION:-latest}
  network_mode: host
  restart: always
  healthcheck:
    test:
      ["CMD", "curl", "-f", "http://localhost:${PORT}/minio/health/live"]
    interval: 30s
    timeout: 20s
    retries: 3

services:
  minio_node:
    container_name: minio
    <<: *minio-common
    extra_hosts:
      - "minio1:${MINIO1_HOST}"
      - "minio2:${MINIO2_HOST}"
      - "minio3:${MINIO3_HOST}"
      - "minio4:${MINIO4_HOST}"
    command: >
      server --console-address ":${CONSOLE_PORT:-9001}" --address ":${PORT}"
      http://minio{1...4}:${PORT}/data{1...9}
    environment:
      MINIO_ROOT_USER: $MINIO_ROOT_USER
      MINIO_ROOT_PASSWORD: $MINIO_ROOT_PASSWORD
      MINIO_SERVER_URL: $MINIO_SERVER_URL
      MINIO_PROMETHEUS_AUTH_TYPE: public
      MINIO_BROWSER_REDIRECT_URL: $MINIO_BROWSER_REDIRECT_URL
      MINIO_NOTIFY_ELASTICSEARCH_ENABLE_PRIMARY: "on"
      MINIO_NOTIFY_ELASTICSEARCH_URL_PRIMARY: ${ELASTICSEARCH_URL}
      MINIO_NOTIFY_ELASTICSEARCH_INDEX_PRIMARY: "minio"
    volumes:
      - type: bind
        source: "${MINIO_DATA}/lvb"
        target: /data1
      - type: bind
        source: "${MINIO_DATA}/lvc"
        target: /data2
      - type: bind
        source: "${MINIO_DATA}/lvd"
        target: /data3
      - type: bind
        source: "${MINIO_DATA}/lve"
        target: /data4
      - type: bind
        source: "${MINIO_DATA}/lvf"
        target: /data5
      - type: bind
        source: "${MINIO_DATA}/lvg"
        target: /data6
      - type: bind
        source: "${MINIO_DATA}/lvh"
        target: /data7
      - type: bind
        source: "${MINIO_DATA}/lvi"
        target: /data8
      - type: bind
        source: "${MINIO_DATA}/lvj"
        target: /data9