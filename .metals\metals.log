2023.08.24 14:16:37 INFO  Started: Metals version 1.0.0 in folders '/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts' for client Visual Studio Code 1.81.1.
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.license.VersionPrinter printVersionOnly
信息: Flyway Community Edition 9.20.1 by Redgate
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.license.VersionPrinter printVersion
信息: See release notes here: https://rd.gt/416ObMi
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.license.VersionPrinter printVersion
信息: 
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.database.base.BaseDatabaseType createDatabase
信息: Database: jdbc:h2:file:/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts/.metals/metals (H2 2.1)
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory allAppliedMigrations
信息: Schema history table "PUBLIC"."flyway_schema_history" does not exist yet
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.command.DbValidate validate
信息: Successfully validated 4 migrations (execution time 00:00.007s)
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory create
信息: Creating Schema History table "PUBLIC"."flyway_schema_history" ...
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.command.DbMigrate migrateGroup
信息: Current version of schema "PUBLIC": << Empty Schema >>
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.command.DbMigrate doMigrateGroup
信息: Migrating schema "PUBLIC" to version "1 - Create tables"
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.command.DbMigrate doMigrateGroup
信息: Migrating schema "PUBLIC" to version "2 - Server discovery"
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.command.DbMigrate doMigrateGroup
信息: Migrating schema "PUBLIC" to version "3 - Jar symbols"
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.command.DbMigrate doMigrateGroup
信息: Migrating schema "PUBLIC" to version "4 - Fingerprints"
8月 24, 2023 2:16:37 下午 org.flywaydb.core.internal.command.DbMigrate logSummary
信息: Successfully applied 4 migrations to schema "PUBLIC", now at version v4 (execution time 00:00.009s)
2023.08.24 14:16:37 INFO  time: initialize in 0.42s
2023.08.24 14:16:38 WARN  Build server is not auto-connectable.
2023.08.24 14:16:38 WARN  no build tool detected in workspace '/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts'. The most common cause for this problem is that the editor was opened in the wrong working directory, for example if you use sbt then the workspace directory should contain build.sbt. 
2023.08.25 10:01:34 INFO  Shutting down server
2023.08.25 10:01:34 INFO  shutting down Metals
2023.08.25 10:01:34 INFO  Exiting server
2023.11.11 14:50:26 INFO  Started: Metals version 1.1.0 in folders 'G:\OneDrive\scripts' for client Visual Studio Code 1.84.2.
2023.11.11 14:50:29 WARN  Build server is not auto-connectable.
2023.11.11 14:50:29 WARN  no build tool detected in workspace 'G:\OneDrive\scripts'. The most common cause for this problem is that the editor was opened in the wrong working directory, for example if you use sbt then the workspace directory should contain build.sbt. 
2024.02.10 12:44:53 INFO  Started: Metals version 1.2.1 in folders '/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts' for client Visual Studio Code 1.86.1.
2024.02.10 12:44:57 INFO  Upgraded H2 database.
12:44:57.764 [pool-1-thread-4] INFO org.flywaydb.core.internal.license.VersionPrinter -- Flyway Community Edition 9.22.3 by Redgate
12:44:57.765 [pool-1-thread-4] INFO org.flywaydb.core.internal.license.VersionPrinter -- See release notes here: https://rd.gt/416ObMi
12:44:57.765 [pool-1-thread-4] INFO org.flywaydb.core.internal.license.VersionPrinter -- 
12:44:57.766 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.classpath.ClassPathScanner -- Scanning for classpath resources at 'classpath:db/callback' ...
12:44:57.766 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.classpath.ClassPathScanner -- Determining location urls for classpath:db/callback using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@531d72ca ...
12:44:57.766 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.classpath.ClassPathScanner -- Unable to resolve location classpath:db/callback.
12:44:57.766 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.classpath.ClassPathScanner -- Scanning for classpath resources at 'classpath:db/migration' ...
12:44:57.766 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.classpath.ClassPathScanner -- Determining location urls for classpath:db/migration using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@531d72ca ...
12:44:57.766 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.classpath.ClassPathScanner -- Scanning URL: jar:file:/Users/<USER>/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/org/scalameta/metals_2.13/1.2.1/metals_2.13-1.2.1.jar!/db/migration
12:44:57.768 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.classpath.ClassPathScanner -- Found resource: db/migration/
12:44:57.768 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.classpath.ClassPathScanner -- Found resource: db/migration/V1__Create_tables.sql
12:44:57.769 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.classpath.ClassPathScanner -- Found resource: db/migration/V2__Server_discovery.sql
12:44:57.769 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.classpath.ClassPathScanner -- Found resource: db/migration/V3__Jar_symbols.sql
12:44:57.769 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.classpath.ClassPathScanner -- Found resource: db/migration/V4__Fingerprints.sql
12:44:57.769 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.classpath.ClassPathScanner -- Scanning for classes at classpath:db/migration
12:44:57.770 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/ (filename: )
12:44:57.770 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.resource.ResourceNameValidator -- Validating V3__Jar_symbols.sql
12:44:57.771 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.resource.ResourceNameValidator -- Validating V4__Fingerprints.sql
12:44:57.771 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.resource.ResourceNameValidator -- Validating V2__Server_discovery.sql
12:44:57.771 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.resource.ResourceNameValidator -- Validating V1__Create_tables.sql
12:44:57.828 [pool-1-thread-4] INFO org.flywaydb.core.FlywayExecutor -- Database: jdbc:h2:file:/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts/.metals/metals (H2 2.2)
12:44:57.828 [pool-1-thread-4] DEBUG org.flywaydb.core.FlywayExecutor -- Driver: H2 JDBC Driver 2.2.224 (2023-09-17)
12:44:57.828 [pool-1-thread-4] DEBUG org.flywaydb.core.FlywayExecutor -- DDL Transactions Supported: false
12:44:57.829 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.schemahistory.SchemaHistoryFactory -- Schemas: 
12:44:57.829 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.schemahistory.SchemaHistoryFactory -- Default schema: null
12:44:57.831 [pool-1-thread-4] WARN org.flywaydb.core.internal.database.base.Database -- Flyway upgrade recommended: H2 2.2.224 is newer than this version of Flyway and support has not been tested. The latest supported version of H2 is 2.2.220.
12:44:57.831 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.callback.SqlScriptCallbackFactory -- Scanning for SQL callbacks ...
12:44:57.831 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/ (filename: )
12:44:57.834 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.command.DbValidate -- Validating migrations ...
12:44:57.836 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/ (filename: )
12:44:57.839 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/ (filename: )
12:44:57.839 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/V3__Jar_symbols.sql (filename: V3__Jar_symbols.sql)
12:44:57.839 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/V4__Fingerprints.sql (filename: V4__Fingerprints.sql)
12:44:57.839 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/V2__Server_discovery.sql (filename: V2__Server_discovery.sql)
12:44:57.839 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/V1__Create_tables.sql (filename: V1__Create_tables.sql)
12:44:57.839 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/ (filename: )
12:44:57.839 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/V3__Jar_symbols.sql (filename: V3__Jar_symbols.sql)
12:44:57.839 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/V4__Fingerprints.sql (filename: V4__Fingerprints.sql)
12:44:57.839 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/V2__Server_discovery.sql (filename: V2__Server_discovery.sql)
12:44:57.839 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.scanner.Scanner -- Filtering out resource: db/migration/V1__Create_tables.sql (filename: V1__Create_tables.sql)
12:44:57.844 [pool-1-thread-4] INFO org.flywaydb.core.internal.command.DbValidate -- Successfully validated 4 migrations (execution time 00:00.009s)
12:44:57.845 [pool-1-thread-4] DEBUG org.flywaydb.core.internal.command.DbSchemas -- Skipping creation of existing schema: "PUBLIC"
12:44:57.847 [pool-1-thread-4] INFO org.flywaydb.core.internal.command.DbMigrate -- Current version of schema "PUBLIC": 4
12:44:57.849 [pool-1-thread-4] INFO org.flywaydb.core.internal.command.DbMigrate -- Schema "PUBLIC" is up to date. No migration necessary.
12:44:58.104 [pool-1-thread-4] DEBUG org.flywaydb.core.FlywayExecutor -- Memory usage: 58 of 125M
2024.02.10 12:44:57 WARN  no build tool detected in workspace '/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts'. The most common cause for this problem is that the editor was opened in the wrong working directory, for example if you use sbt then the workspace directory should contain build.sbt. 
2024.02.10 12:44:57 WARN  Build server is not auto-connectable.
2024.02.10 12:53:03 INFO  Shutting down server
2024.02.10 12:53:03 INFO  shutting down Metals
2024.02.10 12:53:03 INFO  Exiting server
2024.10.18 10:26:12 INFO  Started: Metals version 1.3.5 in folders '/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts' for client Visual Studio Code 1.94.2.
SLF4J(W): Class path contains multiple SLF4J providers.
SLF4J(W): Found provider [scribe.slf4j.ScribeServiceProvider@6f472278]
SLF4J(W): Found provider [ch.qos.logback.classic.spi.LogbackServiceProvider@7b2216eb]
SLF4J(W): See https://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J(I): Actual provider is of type [scribe.slf4j.ScribeServiceProvider@6f472278]
2024.10.18 10:26:13 WARN  Flyway upgrade recommended: H2 2.3.230 is newer than this version of Flyway and support has not been tested. The latest supported version of H2 is 2.2.220.
2024.10.18 10:26:13 WARN  no build tool detected in workspace '/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts'. The most common cause for this problem is that the editor was opened in the wrong working directory, for example if you use sbt then the workspace directory should contain build.sbt. 
2024.10.18 10:26:14 WARN  Build server is not auto-connectable.
10月 18, 2024 10:53:37 上午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
10月 18, 2024 10:54:41 上午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
10月 18, 2024 10:55:26 上午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
10月 18, 2024 2:05:04 下午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
10月 18, 2024 2:05:10 下午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
10月 18, 2024 2:24:07 下午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
10月 18, 2024 3:27:08 下午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
10月 18, 2024 3:55:31 下午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
2024.10.18 17:40:57 INFO  Shutting down server
2024.10.18 17:40:57 INFO  shutting down Metals
2024.10.18 17:40:57 INFO  Exiting server
2024.10.22 11:20:19 INFO  Started: Metals version 1.3.5 in folders '/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts' for client Visual Studio Code 1.94.2.
SLF4J(W): Class path contains multiple SLF4J providers.
SLF4J(W): Found provider [scribe.slf4j.ScribeServiceProvider@2b7f5ae9]
SLF4J(W): Found provider [ch.qos.logback.classic.spi.LogbackServiceProvider@4a1aa6cb]
SLF4J(W): See https://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J(I): Actual provider is of type [scribe.slf4j.ScribeServiceProvider@2b7f5ae9]
2024.10.22 11:20:20 WARN  Flyway upgrade recommended: H2 2.3.230 is newer than this version of Flyway and support has not been tested. The latest supported version of H2 is 2.2.220.
2024.10.22 11:20:20 WARN  no build tool detected in workspace '/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts'. The most common cause for this problem is that the editor was opened in the wrong working directory, for example if you use sbt then the workspace directory should contain build.sbt. 
2024.10.22 11:20:21 WARN  Build server is not auto-connectable.
2024.10.22 13:34:19 INFO  Shutting down server
2024.10.22 13:34:19 INFO  shutting down Metals
2024.10.22 13:34:19 INFO  Exiting server
2024.10.22 13:34:26 INFO  Started: Metals version 1.3.5 in folders '/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts' for client Visual Studio Code 1.94.2.
SLF4J(W): Class path contains multiple SLF4J providers.
SLF4J(W): Found provider [scribe.slf4j.ScribeServiceProvider@678c7bf1]
SLF4J(W): Found provider [ch.qos.logback.classic.spi.LogbackServiceProvider@555f3b1e]
SLF4J(W): See https://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J(I): Actual provider is of type [scribe.slf4j.ScribeServiceProvider@678c7bf1]
2024.10.22 13:34:26 WARN  Flyway upgrade recommended: H2 2.3.230 is newer than this version of Flyway and support has not been tested. The latest supported version of H2 is 2.2.220.
2024.10.22 13:34:26 WARN  no build tool detected in workspace '/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts'. The most common cause for this problem is that the editor was opened in the wrong working directory, for example if you use sbt then the workspace directory should contain build.sbt. 
2024.10.22 13:34:27 WARN  Build server is not auto-connectable.
10月 22, 2024 1:34:35 下午 org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
信息: Unsupported notification method: $/setTrace
2024.10.22 13:34:37 INFO  Shutting down server
2024.10.22 13:34:37 INFO  shutting down Metals
2024.10.22 13:34:37 INFO  Exiting server
