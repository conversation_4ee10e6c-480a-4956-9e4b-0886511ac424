# ap minio/setup_ly -K --extra-vars="host=l1,l2,l3,l4 data_root=/mnt"

- name: Minio Setup
  hosts: "{{ host }}"
  become: yes
  become_user: "{{ user | default('czmp') }}"
  gather_facts: yes
  tasks:
    - name: debug through ansible.env
      debug: var=ansible_env.HOME # ansible_env need gather_facts
    - name: standby directory
      file:
        path: ~/minio # equal
        # or: path: "{{ ansible_env.HOME }}/minio"
        state: directory
        mode: '0755'
    - name: standby data directory
      file: # sudo chown czmp:czmp -R /mnt
        path: "{{ data_root | default('/mnt/data/minio') }}"
        state: directory
        mode: '0777'
    - name: standby certs directory
      file:
        path: ~/.minio/certs
        state: directory
        mode: '0755'
    - name: compose down
      community.docker.docker_compose_v2:
        state: absent
        project_src: ~/minio
      ignore_errors: true
    - name: copy .env
      copy:
        src: ./.env.ly.cluster
        dest: ~/minio/.env
        mode: '0755'
    - name: copy docker-compose.yml
      copy:
        src: ./minio-cluster.ly.compose.yml
        dest: ~/minio/docker-compose.yml
        mode: '0755'
    - name: compose up
      community.docker.docker_compose_v2:
        state: present
        project_src: ~/minio
      register: output
    - name: Show results
      ansible.builtin.debug:
        var: output
