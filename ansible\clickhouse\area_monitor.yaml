# ap clickhouse/area_monitor

- name: select create_time
  hosts: jintan_clickhouse,liyang_clickhouse,xin<PERSON>,wujin
  gather_facts: no
  tasks:
    - shell: >
        clickhouse-client --password CZMP.2022 --port 9030 -nq 
        "select create_time, check_time from cityserver_mater_pg_area.bs_dd_check_weight order by create_time desc limit 1;"
      register: show
    - name: std
      debug: msg="{{ show.stderr }} {{ show.stdout }}"
