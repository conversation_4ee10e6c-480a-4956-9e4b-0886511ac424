# https://kubernetes.io/zh-cn/docs/tasks/configure-pod-container/configure-pod-configmap/#configure-all-key-value-pairs-in-a-configmap-as-container-environment-variables
# kubectl create -f cityserver-envs.yaml

# for update
# kubectl apply -f cityserver-envs.yaml
# kubectl rollout restart deployment/cityserver-deployment
# kubectl rollout restart deployment/cityserver-task-deployment

apiVersion: v1
kind: ConfigMap
metadata:
  name: cityserver-envs
data:
  _JAVA_OPTIONS: -Xmx8g -Xms2g --add-exports=java.base/sun.security.action=ALL-UNNAMED # -javaagent:/var/lib/opentelemetry-javaagent.jar
  # - JHIPSTER_SLEEP=30 # gives time for other services to boot before the application
  # - SPRING_DATASOURCE_URL=************************************,*************:5432,*************:5432,*************:5432/czmpcity?loadBalanceHosts=true
  SPRING_LIQUIBASE_URL: *********************************************
  SPRING_LIQUIBASE_USER: czmpcity
  SPRING_LIQUIBASE_PASSWORD: CZMP^2023
#   ZIPKIN_BASE_URL: http://*************:9411
#   OTEL_METRICS_EXPORTER: none
#   OTEL_EXPORTER_OTLP_ENDPOINT: http://*************:4317
#   OTEL_RESOURCE_ATTRIBUTES: service.name=czmpcityserver
# volumes:
#   - type: bind
#     source: ~/lib/opentelemetry-javaagent.jar
#     target: /var/lib/opentelemetry-javaagent.jar
