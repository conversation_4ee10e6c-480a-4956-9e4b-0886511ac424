echo "city_rebuild start! $(date)" > /var/log/city_rebuild.log
# /home/<USER>/.local/bin/ansible-playbook -i /var/lib/ansible/inventory.yaml /var/lib/ansible/city_rebuild.yaml 1> /dev/null 2> /var/log/city_rebuild.log
/home/<USER>/.local/bin/ansible-playbook -i /var/lib/ansible/inventory.yaml /var/lib/ansible/city_rebuild.yaml > /var/log/city_rebuild.log
# drop unbuffer/ts, 不输出error!

# sudo touch /var/log/city_rebuild.log
# sudo chown czmp:czmp /var/log/city_rebuild.log
# sudo chmod +x /var/lib/ansible/city_rebuild.sh
# tail -f /var/log/city_rebuild.log
# > /var/log/city_rebuild.log

# crontab -e
# 0 19 * * * /var/lib/ansible/city_rebuild.sh