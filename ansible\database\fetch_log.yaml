# ap database/fetch_log -e="host=city_citus"
# ap database/fetch_log -e="host=y2"
# ap database/fetch_log -e="host=jintan_pg"
# ap database/fetch_log -e="host=liyang_pg"
# ap database/fetch_log -e="host=wujin"
# ap database/fetch_log -e="host=xinbei"
# ap database/fetch_log -e="host=points"

# ap database/fetch_log -e="host=city_citus,y2,jintan_pg,liyang_pg,wujin,xinbei"

- name: Fetch Log
  hosts: "{{ host }}"
  # become: yes
  vars:
    pg_version_map:
      y2: 16
      l3: 16
      p8: 16
      p11: 16
      p14: 16
      p15: 16
      p21: 16
      p22: 16
      p23: 16
      p26: 16
      p27: 16
      p31: 16
      p32: 16
      default: 15

  tasks:
    - set_fact:
        # current_date: "{{ lookup('pipe', 'date +%Y-%m-%d') }}"
        pg_version: "{{ pg_version_map[inventory_hostname] | default(pg_version_map['default']) }}"
    - debug:
        msg: "PostgreSQL version: {{ pg_version }}"
    - set_fact:
        log_path: /var/log/postgresql/postgresql-{{ pg_version }}-main.log

    - name: Tail content
      # command: sudo tail -n 500 {{ log_path }}
      shell: tail -n 500 {{ log_path }} > /tmp/pg_log.log
      # register: log_content

    # - name: Save content
    #   copy:
    #     content: "{{ log_content.stdout }}"
    #     dest: ~/pg_logs/{{ current_date }}.log

    - ansible.builtin.fetch:
        src: /tmp/pg_log.log
        dest: ~/work/fetch_data/pg_logs/{{ inventory_hostname }}.log
        flat: true

    - name: Tail error
      shell: tail -n 1000 {{ log_path }} | grep -E 'ERROR|FATAL' > /tmp/pg_error.log || true

    - ansible.builtin.fetch:
        src: /tmp/pg_error.log
        dest: ~/work/fetch_data/pg_errors/{{ inventory_hostname }}.log
        flat: true