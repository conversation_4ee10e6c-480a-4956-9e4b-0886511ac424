# ap rabbitmq/setup -K --extra-vars="host=l2"

- name: Rabbitmq Setup
  hosts: "{{ host }}"
  become: yes
  become_user: root
  gather_facts: no
  tasks:
    - name: absent
      community.docker.docker_container:
        name: rabbitmq
        state: absent
        force_kill: yes
      ignore_errors: true
    - name: push rabbitmq.conf
      copy:
        src: ./rabbitmq.conf
        dest: /var/lib/rabbitmq.conf
        mode: '0755'
    - name: push delayed_message_exchange
      copy:
        src: ./rabbitmq_delayed_message_exchange-3.13.0.ez
        dest: /var/lib/rabbitmq_delayed_message_exchange-3.13.0.ez
        mode: "0755"
    - name: push enabled_plugins
      copy:
        src: ./rabbitmq_enabled_plugins
        dest: /var/lib/rabbitmq_enabled_plugins
        mode: "0755"
    - name: standby mnesia
      file:
        path: /mnt/data/rabbitmq_mnesia
        state: directory
        mode: "0755"
    - name: compose up
      community.docker.docker_compose_v2:
        project_name: rabbitmq
        definition:
          version: "3.8"
          services:
            rabbitmq:
              image: rabbitmq:3.13.3-management
              # image: rabbitmq:3.12.0-management
              container_name: rabbitmq
              restart: always
              environment:
                - RABBITMQ_DEFAULT_USER=czmp
                - RABBITMQ_DEFAULT_PASS=CZMP.2021
                # - RABBITMQ_SERVER_ADDITIONAL_ERL_ARGS="-rabbit listeners.tcp.1 0.0.0.0:5672"
              ports:
                - "5672:5672"
                - "15672:15672"
              volumes:
                - /mnt/data/rabbitmq_mnesia:/var/lib/rabbitmq/mnesia
                - /var/lib/rabbitmq_delayed_message_exchange-3.13.0.ez:/opt/rabbitmq/plugins/rabbitmq_delayed_message_exchange-3.13.0.ez
                # - /var/lib/rabbitmq_delayed_message_exchange-3.12.0.ez:/opt/rabbitmq/plugins/rabbitmq_delayed_message_exchange-3.12.0.ez
                - /var/lib/rabbitmq_enabled_plugins:/etc/rabbitmq/enabled_plugins
                - /var/lib/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      register: output

    - name: Show results
      ansible.builtin.debug:
        var: output

# https://hub.docker.com/_/rabbitmq
# https://www.rabbitmq.com/configure.html#config-items
# https://github.com/rabbitmq/rabbitmq-delayed-message-exchange
