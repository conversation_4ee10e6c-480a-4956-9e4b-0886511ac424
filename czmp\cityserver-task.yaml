apiVersion: apps/v1
kind: Deployment
metadata:
  name: cityserver-task-deployment
  labels:
    platform: city
    kind: task
spec:
  selector:
    matchLabels:
      app: spring-task
  template:
    metadata:
      labels:
        app: spring-task
    spec:
      imagePullSecrets:
        - name: regcred
      hostAliases:
        - ip: "*************"
          hostnames:
          - "www.liquibase.org"
      containers:
      - name: cityserver
        image: registry.cn-shanghai.aliyuncs.com/czmp/czmpcityserver
        imagePullPolicy: "IfNotPresent"
        resources:
          limits:
            memory: 16G
            cpu: 8
        ports:
          - containerPort: 8080
        envFrom:
          - configMapRef:
              name: cityserver-envs
        env:
          - name: SPRING_PROFILES_ACTIVE
            value: prod,task,no-liquibase
