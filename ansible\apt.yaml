# ap apt -K -e="host=cloud"
# ap apt -K -e="host=media"
# ap apt -K -e="host=points_jintan,points_wujin"
# ap apt -K -e="host=points_xinbei"

- name: Apt
  hosts: "{{ host }}"
  become: true
  tasks:
    # - name: apt update
    #   apt:
    #     name: apt
    #     state: latest
    #     update_cache: yes
    # - name: install tailscale
    #   apt:
    #     name: tailscale
    #     state: present
    #     update_cache: yes
    - name: remove cloudflared
      apt:
        name: cloudflared
        state: absent
    - name: remove tailscale
      apt:
        name: tailscale
        state: absent