# pip install pillow
from PIL import ImageFont

def get_line_height(font_path, font_size):
    font = ImageFont.truetype(font_path, font_size)
    ascent, descent = font.getmetrics()
    line_height = ascent + descent
    return line_height

# 获取字体在 12, 22, 48 号字体下的行高
# font_path = "/Users/<USER>/work/czmpmedia/czmpmedia/assets/msyh.ttf"
font_path = "/Users/<USER>/work/czmpmedia/czmpmedia/assets/SourceHanSerifCN-VF.ttf"
font_sizes = [12, 22, 48]

for size in font_sizes:
    line_height = get_line_height(font_path, size)
    print(f"Font size {size} pt: Line height = {line_height} pixels")