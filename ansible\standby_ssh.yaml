# ap standby_ssh -K -e="host=y0,y1,y2,y3,y4,y5,y6"
# ap standby_ssh -K -e="host=m1,m2,m3"

- name: Setup SSH
  hosts: "{{ host }}"
  become: yes
  tasks:
    # https://stackoverflow.com/a/65261020
    - name: set sshd_config
      lineinfile:
        path: /etc/ssh/sshd_config
        regex: "^(#)?{{item.key}}"
        line: "{{item.key}} {{item.value}}"
        state: present
      loop:
        - { key: "ClientAliveInterval", value: "100" }
        - { key: "ClientAliveCountMax", value: "5" }
        - { key: "PermitRootLogin", value: "no" }
        # - { key: "PasswordAuthentication", value: "yes" }
    - name: set pam sshd
      lineinfile:
        path: /etc/pam.d/sshd
        regex: '^auth\s+required\s+pam_faillock.so'
        line: "auth  required  pam_faillock.so  deny=5 unlock_time=600 even_deny_root root_unlock_time=600"
        state: present
        insertbefore: "BOF"
    - name: set pam login
      lineinfile:
        path: /etc/pam.d/login
        regex: '^auth\s+required\s+pam_faillock.so'
        line: "auth  required  pam_faillock.so  deny=5 unlock_time=600 even_deny_root root_unlock_time=600"
        state: present
        insertbefore: "BOF"
    - name: set profile
      lineinfile:
        path: /etc/profile
        regex: "^export TMOUT="
        line: "export TMOUT=500"
        state: present
        insertafter: "EOF"
    - shell: apt update
    - name: Install libpam-cracklib
      apt:
        name: libpam-cracklib
        state: present
    - name: set pam
      lineinfile:
        path: /etc/pam.d/common-password
        regex: '^password\s+requisite\s+pam_cracklib.so'
        line: "password  requisite  pam_cracklib.so  retry=5 minlen=8 difok=5 ucredit=-1 lcredit=-1 dcredit=-1 ocredit=-1"
        state: present
      notify:
        - sshd reload
  handlers:
    - name: sshd reload
      shell: sudo service sshd reload

# only support root inventory, for update pwd,
# not support initial root pwd! pls: sudo passwd root
# - name: Update Root Password
#   hosts: "{{ host }}"
#   tags: [pwd]
#   gather_facts: no
#   vars:
#     password: "czmp.2021.ly"
#     # password: "czmp.2026"
#   tasks:
#     - name: change password
#       ansible.builtin.user:
#         name: root
#         state: present
#         update_password: always
#         password: "{{ password | password_hash('sha512') }}"

# changr port: https://serverfault.com/a/1159600/968626