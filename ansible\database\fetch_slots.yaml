# ap database/entry -t city --extra-vars="play=fetch_slots"
# ap database/entry -t area --extra-vars="play=fetch_slots"

- name: Fetch Slots
  hosts: "{{ inventory }}"
  gather_facts: no
  tasks:
    - postgresql_query:
        login_host: 127.0.0.1
        db: "{{ db }}"
        login_password: "{{ login_password }}"
        port: "{{ pg_port | default(5432) | int }}"
        autocommit: yes
        query: >
          SELECT slot_name,active,database,wal_status FROM pg_replication_slots;
      register: result

    - name: Save result
      ansible.builtin.copy:
        content: "{{ result.query_result | to_nice_json }}"
        dest: /tmp/pg_slots.json

    - ansible.builtin.fetch:
        src: /tmp/pg_slots.json
        dest: ~/work/fetch_data/pg_slots/{{ inventory_hostname }}.json
        flat: true