# https://kubernetes.io/zh-cn/docs/tasks/configure-pod-container/configure-pod-configmap/#configure-all-key-value-pairs-in-a-configmap-as-container-environment-variables
# kubectl create -f cityserver-chpg-envs.yaml
# update:
# kubectl apply -f cityserver-chpg-envs.yaml
# kubectl rollout restart deployment/cityserver-chpg-deployment

apiVersion: v1
kind: ConfigMap
metadata:
  name: cityserver-chpg-envs
data:
  _JAVA_OPTIONS: -Xmx8g -Xms2g # -javaagent:/var/lib/opentelemetry-javaagent.jar
  # - JHIPSTER_SLEEP=30 # gives time for other services to boot before the application
  SPRING_DATASOURCE_URL: jdbc:postgresql://*************:9005/cityserver_dist?sslmode=disable&preferQueryMode=simple
  SPRING_DATASOURCE_USERNAME: default
  SPRING_DATASOURCE_PASSWORD: CZMP.2022
