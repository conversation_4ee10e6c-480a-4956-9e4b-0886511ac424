# ap standby_pip -K --extra-vars="host=points_jintan"
# ap standby_pip -K --extra-vars="host=p27"
# ap standby_pip -K --extra-vars="host=fmc user=code"
# https://askubuntu.com/a/1469197/1828437

- name: Setup Pip
  hosts: "{{ host }}"
  become: yes
  tasks:
    - name: Apt Update
      shell: apt update
    - name: Install python3-pip
      apt:
        name: python3-pip
        state: present
    - name: Install python3-venv
      apt:
        name: python3-venv
        state: present
    - name: Install sshpass
      apt:
        name: sshpass
        state: present

- name: Standby Pip
  hosts: "{{ host }}"
  become: yes
  become_user: "{{ user | default('czmp') }}" # otherwise `~` locate to /root
  tasks:
    - name: ~/.pip
      file:
        path: ~/.pip
        state: directory
    - name: pip.conf
      copy:
        dest: ~/.pip/pip.conf
        content: |
          [global] 
          index-url=https://pypi.tuna.tsinghua.edu.cn/simple 
          [install] 
          trusted-host=mirrors.aliyun.com
    - name: set venv
      shell: python3 -m venv ~/.venv
    - name: venv pip.conf
      copy:
        dest: ~/.venv/pip.conf
        content: |
          [global] 
          index-url=https://pypi.tuna.tsinghua.edu.cn/simple 
          [install] 
          trusted-host=mirrors.aliyun.com

    # # If using community.docker.docker_compose_v2, don't need these
    # - name: pip install docker
    #   pip:
    #     name: docker==6.1.3 # https://stackoverflow.com/a/77651191/346701
    #     virtualenv: ~/.venv
    # - name: pip install pyyaml
    #   pip:
    #     name: pyyaml==5.3.1 # https://github.com/docker/compose/issues/11168#issuecomment-1800362132
    #     virtualenv: ~/.venv
    # - name: pip install docker-compose
    #   pip:
    #     name: docker-compose
    #     virtualenv: ~/.venv
    # # end

    - name: Install pip psycopg2
      apt:
        name: python3-psycopg2
        state: present

    - name: Install pip redis
      apt:
        name: python3-redis
        state: present
