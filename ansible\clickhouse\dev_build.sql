SET allow_experimental_database_materialized_postgresql=1;

CREATE DATABASE cityserver_mater_pg 
ENGINE = MaterializedPostgreSQL('127.0.0.1:5432', 'CzmpCityServer', 'postgres', 'CZMP.1019') 
SETTINGS 
materialized_postgresql_tables_list = 'bs_dd_check_weight,bs_dd_vehicle_info,alerting', 
materialized_postgresql_max_block_size=1;

CREATE DATABASE cityserver_mater_pg_area
ENGINE = MaterializedPostgreSQL('127.0.0.1:5432', 'czmparea', 'postgres', 'CZMP.1019') 
SETTINGS 
materialized_postgresql_tables_list = 'bs_dd_check_weight,bs_dd_vehicle_info,alerting', 
materialized_postgresql_max_block_size=1;

CREATE DATABASE cityserver_mater_pg_point
ENGINE = MaterializedPostgreSQL('127.0.0.1:5432', 'czmppoint', 'czmppoint', 'CZMP.2019') 
SETTINGS 
materialized_postgresql_tables_list = 'bs_dd_check_weight,bs_dd_vehicle_info,alerting', 
materialized_postgresql_max_block_size=1;

CREATE DATABASE cityserver_mater_pg 
ENGINE = MaterializedPostgreSQL('127.0.0.1:5432', 'czmpcityserver', 'postgres', 'z5z5') 
SETTINGS 
materialized_postgresql_tables_list = 'bs_dd_check_weight,bs_dd_vehicle_info,alerting', 
materialized_postgresql_max_block_size=1;