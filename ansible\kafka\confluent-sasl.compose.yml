# https://medium.com/@hussein.joe.au/kafka-authentication-using-sasl-scram-740e55da1fbc
# https://github.com/hussein-joe/kafka-security-ssl-sasl/blob/master/sasl-scram/docker-compose-scram.yml
# https://github.com/confluentinc/cp-demo/blob/7.3.0-post/docker-compose.yml

services:
  zookeeper:
    container_name: zookeeper
    image: confluentinc/cp-zookeeper:${CONFLUENT_VERSION:-7.6.1}
    restart: always
    healthcheck:
      test: echo srvr | nc zookeeper 2181 || exit 1
      retries: 20
      interval: 10s
    ports:
      - 2181:2181
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      KAFKA_OPTS: >-
        -Djava.security.auth.login.config=/etc/zookeeper/zookeeper_jaas.conf
        -Dzookeeper.authProvider.1=org.apache.zookeeper.server.auth.SASLAuthenticationProvider
        -Dzookeeper.authProvider.2=org.apache.zookeeper.server.auth.DigestAuthenticationProvider
        -Dquorum.auth.enableSasl=true
        -DrequireClientAuthScheme=sasl
      # -Dzookeeper.serverCnxnFactory=org.apache.zookeeper.server.NettyServerCnxnFactory
      # -Dquorum.auth.learner.loginContext=QuorumLearner
      # -Dquorum.auth.server.loginContext=QuorumServer
      # -Dquorum.auth.learnerRequireSasl=true
      # -Dquorum.auth.serverRequireSasl=true
      # -Dquorum.cnxn.threads.size=20
      # -DjaasLoginRenew=3600000
    volumes:
      - type: bind
        source: ./zookeeper_jaas.conf
        target: /etc/zookeeper/zookeeper_jaas.conf
        read_only: true
      - type: bind
        source: $ZOO_DATA
        target: /var/lib/zookeeper/data
      - type: bind
        source: $ZOO_SECRETS
        target: /etc/zookeeper/secrets
      # - type: bind
      #   source: $ZOO_LOG
      #   target: /var/lib/zookeeper/log
      # - type: bind
      #   source: $KAFKA_SECRETS
      #   target: /etc/kafka/secrets

  kafka:
    image: confluentinc/cp-kafka:${CONFLUENT_VERSION:-7.6.1}
    # image: wurstmeister/kafka
    container_name: kafka
    restart: always
    hostname: kafka
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - zookeeper
    ports:
      - "${EXTERNAL_PORT:-9092}:9092"
      - "29092:29092"
      - "9997:9997"
    environment:
      KAFKA_HEAP_OPTS: $HEAP_OPTS
      # KAFKA_JVM_PERFORMANCE_OPTS: " ${HEAP_OPTS}" # for wurstmeister/kafka
      KAFKA_BROKER_ID: $BROKER_ID
      KAFKA_ZOOKEEPER_CONNECT: ${ZOOKEEPER_HOST:-zookeeper}:2181
      KAFKA_MESSAGE_MAX_BYTES: $MESSAGE_MAX_BYTES
      KAFKA_REPLICA_FETCH_MAX_BYTES: $REPLICA_FETCH_MAX_BYTES
      KAFKA_DEFAULT_REPLICATION_FACTOR: $REPLICATION_FACTOR
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: $REPLICATION_FACTOR # https://kafka.apache.org/documentation/#upgrade_1100_notable
      KAFKA_NUM_PARTITIONS: $NUM_PARTITIONS
      KAFKA_OFFSETS_TOPIC_NUM_PARTITIONS: $NUM_PARTITIONS

      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: INSIDE:SASL_PLAINTEXT,EXTERNAL:SASL_PLAINTEXT # listener名称与协议的映射,取值:PLAINTEXT,SSL,SASL_PLAINTEXT,SASL_SSL
      KAFKA_INTER_BROKER_LISTENER_NAME: INSIDE # broker之间进行通信时采用的listener名称
      KAFKA_LISTENERS: INSIDE://:29092,EXTERNAL://:9092 # 默认0.0.0.0:29092,0.0.0.0:9092 for wurstmeister/kafka

      # 提交给clients的元数据 https://docs.confluent.io/current/kafka/multi-node.html
      # 注意不要使用'kafka'作为hostname，这样无法跨docker dns部署分布式节点
      # about advertised: http://www.devtalking.com/articles/kafka-practice-16/
      KAFKA_ADVERTISED_LISTENERS: INSIDE://${INSIDE_IP:-host.docker.internal}:29092,EXTERNAL://${EXTERNAL_IP:-host.docker.internal}:${EXTERNAL_PORT:-9092}
      KAFKA_ADVERTISED_HOST_NAME: ${INSIDE_IP:-host.docker.internal} # for cluster identify

      # https://docs.confluent.io/current/installation/docker/operations/monitoring.html
      KAFKA_JMX_PORT: 49999 # for confluentinc/cp-kafka
      JMX_PORT: 49997 # as KAFKA_JMX_OPTS in wurstmeister/kafka. but also detected in cp-kafka as jre jmx. so must difference to KAFKA_JMX_PORT.
      KAFKA_JMX_HOSTNAME: ${INSIDE_IP:-host.docker.internal}

      KAFKA_OPTS: >-
        -Djava.security.auth.login.config=/etc/kafka/kafka_jaas.conf
        -Dzookeeper.sasl.client=true
        -Dzookeeper.sasl.clientconfig=Client

      KAFKA_SASL_ENABLED_MECHANISMS: PLAIN,SCRAM-SHA-256
      # KAFKA_SECURITY_INTER_BROKER_PROTOCOL: SASL_PLAINTEXT # or KAFKA_INTER_BROKER_LISTENER_NAME, only need one
      KAFKA_SASL_MECHANISM_INTER_BROKER_PROTOCOL: PLAIN # SCRAM-SHA-256
      KAFKA_SUPER_USERS: "User:admin;User:cluster"
      KAFKA_AUTHORIZER_CLASS_NAME: "kafka.security.authorizer.AclAuthorizer" # https://stackoverflow.com/a/70582805/346701
      KAFKA_ZOOKEEPER_SASL_ENABLED: "true"
      KAFKA_ZOOKEEPER_SET_ACL: "true"
      KAFKA_ALLOW_EVERYONE_IF_NO_ACL_FOUND: "false"
      KAFKA_LOG_RETENTION_MS: 172800000 # 2days

      # KAFKA_SSL_ENDPOINT_IDENTIFICATION_ALGORITHM: ""
      # KAFKA_SSL_CLIENT_AUTH: requested
      # KAFKA_SSL_KEYSTORE_FILENAME: kafka.kafka1.keystore.jks
      # KAFKA_SSL_KEYSTORE_CREDENTIALS: kafka1_keystore_creds
      # KAFKA_SSL_KEY_CREDENTIALS: kafka1_sslkey_creds
      # KAFKA_SSL_TRUSTSTORE_FILENAME: kafka.kafka1.truststore.jks
      # KAFKA_SSL_TRUSTSTORE_CREDENTIALS: kafka1_truststore_creds
      # KAFKA_SSL_CIPHER_SUITES: ${SSL_CIPHER_SUITES}

      # 自定义任何Kafka参数，只需将它们转换为环境变量
      # 关闭 automatic topic creation：KAFKA_AUTO_CREATE_TOPICS_ENABLE：'false',
      # 启动时创建 topic: KAFKA_CREATE_TOPICS: "Topic1:1:3,Topic2:1:1:compact"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock # https://github.com/wurstmeister/kafka-docker/wiki#why-is-varrundockersock-needed
      # 如果使用 type: volume. docker-compose down 会保留 data. docker-compose down -v 会清理挂载的volume. 且很难自定义host路径
      - type: bind
        source: ./kafka_jaas.conf
        target: /etc/kafka/kafka_jaas.conf
        read_only: true
      - type: bind
        source: $KAFKA_DATA
        target: /var/lib/kafka/data # https://cutt.ly/cYufNO4
      - type: bind
        source: $KAFKA_SECRETS
        target: /etc/kafka/secrets

  schema-registry:
    image: confluentinc/cp-schema-registry:${CONFLUENT_VERSION:-7.6.1}
    container_name: schema_registry
    restart: always
    depends_on:
      - kafka
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - "8085:8085"
    environment:
      SCHEMA_REGISTRY_KAFKASTORE_BOOTSTRAP_SERVERS: "SASL_PLAINTEXT://kafka:29092"
      SCHEMA_REGISTRY_HOST_NAME: "${EXTERNAL_IP:-schema-registry}"
      SCHEMA_REGISTRY_LOG4J_ROOT_LOGLEVEL: "INFO"
      SCHEMA_REGISTRY_LISTENERS: "http://0.0.0.0:8085"

      SCHEMA_REGISTRY_KAFKASTORE_SECURITY_PROTOCOL: SASL_PLAINTEXT
      SCHEMA_REGISTRY_KAFKASTORE_SASL_MECHANISM: PLAIN
      SCHEMA_REGISTRY_KAFKASTORE_SASL_JAAS_CONFIG: |
        org.apache.kafka.common.security.plain.PlainLoginModule required \
        username="${SASL_USERNAME}" \
        password="${SASL_PASSWORD}";

  connect:
    image: confluentinc/cp-kafka-connect:${CONFLUENT_VERSION:-7.6.1}
    restart: always
    container_name: connect
    depends_on:
      - kafka
      - schema-registry
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      CONNECT_BOOTSTRAP_SERVERS: "SASL_PLAINTEXT://kafka:29092"
      CONNECT_REST_PORT: "8083"
      CONNECT_REST_LISTENERS: "http://0.0.0.0:8083"
      CONNECT_REST_ADVERTISED_HOST_NAME: "connect"
      CONNECT_CONFIG_STORAGE_TOPIC: "__connect-config"
      CONNECT_OFFSET_STORAGE_TOPIC: "__connect-offsets"
      CONNECT_STATUS_STORAGE_TOPIC: "__connect-status"
      CONNECT_GROUP_ID: "kafka-connect"
      CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE: "true"
      CONNECT_KEY_CONVERTER: "io.confluent.connect.avro.AvroConverter"
      CONNECT_KEY_CONVERTER_SCHEMA_REGISTRY_URL: "http://schema-registry:8085"
      CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE: "true"
      CONNECT_VALUE_CONVERTER: "io.confluent.connect.avro.AvroConverter"
      CONNECT_VALUE_CONVERTER_SCHEMA_REGISTRY_URL: "http://schema-registry:8085"
      CONNECT_INTERNAL_KEY_CONVERTER: "org.apache.kafka.connect.json.JsonConverter"
      CONNECT_INTERNAL_VALUE_CONVERTER: "org.apache.kafka.connect.json.JsonConverter"
      CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR: "1"
      CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR: "1"
      CONNECT_STATUS_STORAGE_REPLICATION_FACTOR: "1"
      CONNECT_PLUGIN_PATH: " /usr/share/java/"

      CONNECT_SECURITY_PROTOCOL: SASL_PLAINTEXT
      CONNECT_SASL_MECHANISM: PLAIN
      CONNECT_SASL_JAAS_CONFIG: |
        org.apache.kafka.common.security.plain.PlainLoginModule required \
        username="${SASL_USERNAME}" \
        password="${SASL_PASSWORD}";

  # https://github.com/redpanda-data/console/blob/master/docs/local/docker-compose.yaml
  redpanda:
    image: docker.redpanda.com/redpandadata/console:v2.6.0
    # image: docker.redpanda.com/vectorized/console:latest
    restart: on-failure
    container_name: redpanda
    extra_hosts:
      - "host.docker.internal:host-gateway"
    entrypoint: /bin/sh
    command: -c "echo \"$$CONSOLE_CONFIG_FILE\" > /tmp/config.yml; /app/console"
    environment:
      CONFIG_FILEPATH: /tmp/config.yml
      CONSOLE_CONFIG_FILE: |
        kafka:
          brokers: ["kafka:29092"]
          schemaRegistry:
            enabled: true
            urls: ["http://schema-registry:8085"]
        connect:
          enabled: true
          clusters:
            - name: datagen
              url: http://connect:8083
      KAFKA_SASL_ENABLED: "true"
      KAFKA_SASL_USERNAME: $SASL_USERNAME
      KAFKA_SASL_PASSWORD: $SASL_PASSWORD
    ports:
      - 8087:8080
    depends_on:
      - kafka
      - schema-registry
      - connect

  # kafka-ui:
  #   container_name: kafka_ui
  #   image: provectuslabs/kafka-ui:latest
  #   ports:
  #     - 8086:8080
  #   depends_on:
  #     - kafka
  #     - schema-registry
  #     - connect
  #   environment:
  #     KAFKA_CLUSTERS_0_NAME: local
  #     KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: SASL_PLAINTEXT://kafka:29092
  #     KAFKA_CLUSTERS_0_METRICS_PORT: 9997
  #     KAFKA_CLUSTERS_0_SCHEMAREGISTRY: http://schema-registry:8085
  #     KAFKA_CLUSTERS_0_KAFKACONNECT_0_NAME: first
  #     KAFKA_CLUSTERS_0_KAFKACONNECT_0_ADDRESS: http://connect:8083
  #     KAFKA_CLUSTERS_0_PROPERTIES_SECURITY_PROTOCOL: SASL_PLAINTEXT
  #     KAFKA_CLUSTERS_0_PROPERTIES_SASL_MECHANISM: PLAIN
  #     KAFKA_CLUSTERS_0_PROPERTIES_SASL_JAAS_CONFIG: 'org.apache.kafka.common.security.plain.PlainLoginModule required username="${SASL_USERNAME}" password="${SASL_PASSWORD}";'
