# ap pm2_startup -K -e="host=points_jintan,points_wujin"
# ap pm2_startup -K -e="host=points_xinbei user=xbzc"

- name: pm2 startup
  hosts: "{{ host }}"
  become: yes
  tasks:
    - shell: pm2 startup
      register: show2
    - name: std startup
      debug: msg="{{ show2.stderr }} {{ show2.stdout }}"

    - shell: "sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u {{ user }} --hp /home/<USER>"
      register: show3
    - name: std cmd
      debug: msg="{{ show3.stderr }} {{ show3.stdout }}"
      
    - shell: pm2 save
      register: show4
    - name: std save
      debug: msg="{{ show4.stderr }} {{ show4.stdout }}"

    - shell: pm2 install pm2-logrotate
      register: show5
    - name: install pm2-logrotate
      debug: msg="{{ show5.stderr }} {{ show5.stdout }}"

    - shell: |
        pm2 set pm2-logrotate:max_size 100M
        pm2 set pm2-logrotate:retain 50
        pm2 set pm2-logrotate:compress true
      register: show6
    - name: set pm2-logrotate
      debug: msg="{{ show6.stderr }} {{ show6.stdout }}"