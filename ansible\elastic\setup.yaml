# ap elastic/setup -K --extra-vars="host=t1"
# ap elastic/setup -K --extra-vars="host=t1 data_root=~/czmp/elastic_data"
# ap elastic/setup -K --extra-vars="host=l1 data_root=/mnt/lva/elastic"

- name: Elastic Setup
  hosts: "{{ host }}"
  become: yes
  become_user: czmp
  gather_facts: yes
  tasks:
    - name: debug through ansible.env
      debug: var=ansible_env.HOME # ansible_env need gather_facts
    - name: standby directory
      file:
        path: ~/{{ item.project }} # equal
        # or: path: "{{ ansible_env.HOME }}/{{ item.project }}"
        state: directory
        mode: '0755'
      loop:
        - { project: elastic }
        - { project: logstash }
        - { project: kibana }
    - name: standby data directory
      file:
        path: "{{ data_root | default('/mnt/data/elastic') }}"
        state: directory
        mode: '0777'
    - name: compose down
      community.docker.docker_compose_v2:
        state: absent
        project_src: ~/{{ item.project }}
      loop:
        - { project: elastic }
        - { project: logstash }
        - { project: kibana }
      ignore_errors: true
    - name: copy .env
      copy:
        src: ./.env
        dest: ~/{{ item.project }}/.env
        mode: '0755'
      loop:
        - { project: elastic }
        - { project: logstash }
        - { project: kibana }
    - name: copy elastic.compose.yml
      copy:
        src: ./elastic.compose.yml
        dest: ~/elastic/docker-compose.yml
        mode: '0755'
    - name: copy logstash.compose.yml
      copy:
        src: ./logstash.compose.yml
        dest: ~/logstash/docker-compose.yml
        mode: '0755'
    - name: copy kibana.compose.yml
      copy:
        src: ./kibana.compose.yml
        dest: ~/kibana/docker-compose.yml
        mode: '0755'
    - name: copy pipeline.conf
      copy:
        src: ./pipeline.conf
        dest: ~/logstash/pipeline.conf
        mode: "0755"
    - name: compose up
      community.docker.docker_compose_v2:
        state: present
        project_src: ~/{{ item.project }}
      loop:
        - { project: elastic }
        - { project: logstash }
        - { project: kibana }
      register: output
    - name: Show results
      ansible.builtin.debug:
        var: output

# https://docs.ansible.com/ansible/latest/collections/community/docker/docker_compose_module.html