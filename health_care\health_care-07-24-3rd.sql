/*
 Navicat Premium Dump SQL

 Source Server         : localhost-mysql
 Source Server Type    : MySQL
 Source Server Version : 80039 (8.0.39)
 Source Host           : localhost:3306
 Source Schema         : health_care

 Target Server Type    : MySQL
 Target Server Version : 80039 (8.0.39)
 File Encoding         : 65001

 Date: 24/07/2025 10:51:35
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for app_device
-- ----------------------------
DROP TABLE IF EXISTS `app_device`;
CREATE TABLE `app_device`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `app_user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `device_type_id` int NULL DEFAULT NULL COMMENT '设备类型ID',
  `device_id` bigint NULL DEFAULT NULL COMMENT '设备ID',
  `imei` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备唯一编码',
  `is_disabled` tinyint NULL DEFAULT 0 COMMENT '状态是否禁用(0否1是)',
  `is_online` tinyint NULL DEFAULT NULL COMMENT '设备是否在线(0否1是)',
  `is_binding` tinyint NULL DEFAULT NULL COMMENT '是否绑定(0否1是)',
  `longitude` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '经度',
  `latitude` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '纬度',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `created_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = 'app用户设备' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for app_device_alarm
-- ----------------------------
DROP TABLE IF EXISTS `app_device_alarm`;
CREATE TABLE `app_device_alarm`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `device_id` bigint NULL DEFAULT NULL COMMENT '报警设备ID',
  `app_user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `imei` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报警设备编号',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备报警信息',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `device_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备名称',
  `state` tinyint NULL DEFAULT NULL COMMENT '消息处理状态(0未处理,1已处理,2待处理)',
  `solve_time` datetime NULL DEFAULT NULL COMMENT '解决时间',
  `solve_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理人',
  `solve_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理内容',
  `alarm_time` datetime NULL DEFAULT NULL COMMENT '告警时间',
  `show_status` tinyint NULL DEFAULT NULL COMMENT '是否显示(1,显示-0,不显示)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '设备报警表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for app_device_category
-- ----------------------------
DROP TABLE IF EXISTS `app_device_category`;
CREATE TABLE `app_device_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `device_type_id` int NULL DEFAULT NULL COMMENT '设备类型id',
  `app_user_id` bigint NULL DEFAULT NULL COMMENT '老人id',
  `device_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备类型',
  `sort` int NULL DEFAULT NULL COMMENT '排序值',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `del` tinyint NULL DEFAULT 0 COMMENT '删除(0否1是)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 154 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '设备类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for app_user
-- ----------------------------
DROP TABLE IF EXISTS `app_user`;
CREATE TABLE `app_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_id` int NULL DEFAULT NULL COMMENT '角色ID',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称(注册成功即分配自动序列号)',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名或手机号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '密码(未注册时可以为空)',
  `mobile_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
  `realname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `sex` tinyint NULL DEFAULT NULL COMMENT '性别(1男2女;0未知)',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  `personal_profile` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '个人简介',
  `account_status` tinyint NULL DEFAULT 1 COMMENT '账号状态；1正常，0停用',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '注销时间',
  `del` tinyint NULL DEFAULT 0 COMMENT '删除(0不删除1已删除)',
  `sys_type` tinyint NULL DEFAULT 1 COMMENT '系统标识有多个时候按照逗号拼接',
  `out_online_flag` tinyint NULL DEFAULT NULL COMMENT '是否是外部系统导入）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'app用户账号表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_assessment
-- ----------------------------
DROP TABLE IF EXISTS `elder_assessment`;
CREATE TABLE `elder_assessment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `elder_id` bigint NULL DEFAULT NULL COMMENT '长者ID',
  `assessment_date` date NULL DEFAULT NULL COMMENT '评估日期',
  `assessor_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估人',
  `ability_level` int NULL DEFAULT NULL COMMENT '生活能力等级',
  `care_level` int NULL DEFAULT NULL COMMENT '护理等级(十级)',
  `cognitive_status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '认知状况',
  `physical_status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '身体状况',
  `psychological_status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '心理状况',
  `assessment_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '评估结果',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_elder_id`(`elder_id` ASC) USING BTREE,
  INDEX `idx_assessment_date`(`assessment_date` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '长者评估记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_bill
-- ----------------------------
DROP TABLE IF EXISTS `elder_bill`;
CREATE TABLE `elder_bill`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `elder_id` bigint NULL DEFAULT NULL COMMENT '老人ID',
  `room_id` int NULL DEFAULT NULL COMMENT '房间id',
  `bill_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账单编号',
  `bill_month` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账单月份(yyyy-MM)',
  `item_type` tinyint NULL DEFAULT NULL COMMENT '费用类型：1-床位费，2-护理费，3-餐费，4-水电费，5-仪表类 6-其他',
  `bill_type` tinyint NULL DEFAULT NULL COMMENT '缴费类型：1-预缴费，2-缴费',
  `is_charge` tinyint NULL DEFAULT NULL COMMENT '是否欠费',
  `fee_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '费用',
  `account_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '账户余额',
  `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '总金额',
  `paid_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '已付金额',
  `billing_date` datetime NULL DEFAULT NULL COMMENT '账单日期',
  `due_date` datetime NULL DEFAULT NULL COMMENT '付款截止日期',
  `status` tinyint NULL DEFAULT 0 COMMENT '状态：1-未支付，2-部分支付，3-已付清，4-已取消',
  `bill_discount` float NULL DEFAULT NULL COMMENT '折扣',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `bill_number`(`bill_number` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '账单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_check_in
-- ----------------------------
DROP TABLE IF EXISTS `elder_check_in`;
CREATE TABLE `elder_check_in`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `elder_id` bigint NULL DEFAULT NULL COMMENT '长者ID',
  `contact_id` bigint NULL DEFAULT NULL COMMENT '合同ID',
  `room_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房间号',
  `check_in_date` datetime NULL DEFAULT NULL COMMENT '入住日期',
  `assessment_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入住评估',
  `belongings` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '随身物品',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `status` tinyint NULL DEFAULT NULL COMMENT '入住状态:1待审核，2审核通过未入住,3,已审核,未缴费;4已缴费入住',
  `check_in_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '入住金额',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_elder_id`(`elder_id` ASC) USING BTREE,
  INDEX `idx_check_in_date`(`check_in_date` ASC) USING BTREE,
  INDEX `idx_room_id`(`room_number` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '入住记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_check_out
-- ----------------------------
DROP TABLE IF EXISTS `elder_check_out`;
CREATE TABLE `elder_check_out`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `contact_id` bigint NULL DEFAULT NULL COMMENT '合同ID',
  `elder_id` bigint NULL DEFAULT NULL COMMENT '长者ID',
  `room_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '床位号',
  `check_out_date` datetime NULL DEFAULT NULL COMMENT '退住日期',
  `check_out_reason` tinyint NULL DEFAULT NULL COMMENT '退住原因类型：1-正常退住，2-转院，3-死亡，4-其他',
  `approval_status` tinyint NULL DEFAULT 0 COMMENT '审批状态：0-待审批，1-已批准，已退房2-已拒绝',
  `refund_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '退费金额',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_elder_id`(`elder_id` ASC) USING BTREE,
  INDEX `idx_check_out_date`(`check_out_date` ASC) USING BTREE,
  INDEX `idx_approval_status`(`approval_status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退住记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_contract
-- ----------------------------
DROP TABLE IF EXISTS `elder_contract`;
CREATE TABLE `elder_contract`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '合同ID',
  `contract_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同编号',
  `elder_id` bigint NULL DEFAULT NULL COMMENT '长者ID',
  `start_date` date NULL DEFAULT NULL COMMENT '合同开始日期',
  `end_date` date NULL DEFAULT NULL COMMENT '合同结束日期',
  `deposit` decimal(10, 2) NULL DEFAULT NULL COMMENT '押金',
  `payment_method` tinyint NULL DEFAULT NULL COMMENT '付款方式：1-月付，3-季付，6-半年付，12-年付',
  `signed_date` date NULL DEFAULT NULL COMMENT '签订日期',
  `contract_file` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同文件路径',
  `file_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '真实图片',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0-已终止，1-生效中，2-已到期',
  `termination_date` date NULL DEFAULT NULL COMMENT '终止日期',
  `termination_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '终止原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `contract_number`(`contract_number` ASC) USING BTREE,
  INDEX `idx_contract_number`(`contract_number` ASC) USING BTREE,
  INDEX `idx_elder_id`(`elder_id` ASC) USING BTREE,
  INDEX `idx_start_date`(`start_date` ASC) USING BTREE,
  INDEX `idx_end_date`(`end_date` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_dinner_category
-- ----------------------------
DROP TABLE IF EXISTS `elder_dinner_category`;
CREATE TABLE `elder_dinner_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0-停用，1-启用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜品分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_dinner_record
-- ----------------------------
DROP TABLE IF EXISTS `elder_dinner_record`;
CREATE TABLE `elder_dinner_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `elder_id` bigint NULL DEFAULT NULL COMMENT '长者ID',
  `dish_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '菜名称/类型id',
  `meal_time` tinyint NULL DEFAULT NULL COMMENT '餐别：1-早餐，2-午餐，3-晚餐，4-加餐',
  `dinner_time` datetime NULL DEFAULT NULL COMMENT '具体用餐时间',
  `dinner_mode` tinyint NULL DEFAULT NULL COMMENT '就餐方式：1-堂食，2-外带',
  `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '消费总金额',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '用餐详细记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_health_daily_data
-- ----------------------------
DROP TABLE IF EXISTS `elder_health_daily_data`;
CREATE TABLE `elder_health_daily_data`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `elder_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `body_temperature` float NULL DEFAULT NULL COMMENT '体温',
  `light_sleep_time` int NULL DEFAULT NULL COMMENT '浅度睡眠时间(分钟)',
  `deep_sleep_time` int NULL DEFAULT NULL COMMENT '深度睡眠时间(分钟)',
  `steps` int NULL DEFAULT NULL COMMENT '今天行走步数',
  `heart_rate` int NULL DEFAULT NULL COMMENT '心率',
  `diastolic_pressure` float NULL DEFAULT NULL COMMENT '舒张压',
  `systolic_pressure` float NULL DEFAULT NULL COMMENT '收缩压',
  `blood_glucose` float NULL DEFAULT NULL COMMENT '血糖',
  `blood_oxygen` float NULL DEFAULT NULL COMMENT '血氧',
  `location_address` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '定位地址',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户健康检测数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_health_info
-- ----------------------------
DROP TABLE IF EXISTS `elder_health_info`;
CREATE TABLE `elder_health_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `elder_id` bigint NULL DEFAULT NULL COMMENT 'app_user用户ID',
  `blood_type` tinyint NULL DEFAULT NULL COMMENT '血型',
  `health_detail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '健康状况',
  `health_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '健康标签',
  `action_capability` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生活能力',
  `self_care_ability` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '自理能力',
  `care_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '护理等级',
  `sleep_quality` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '睡眠质量',
  `smoking_frequency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '吸烟频率',
  `drinking_frequency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '饮酒频率',
  `exercise_frequency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '运动频率',
  `food_preference` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '饮食偏好',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `del` tinyint NULL DEFAULT 0 COMMENT '删除(0否1是)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户健康数据详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_info
-- ----------------------------
DROP TABLE IF EXISTS `elder_info`;
CREATE TABLE `elder_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `elder_id` bigint NULL DEFAULT NULL COMMENT 'app_user表的id',
  `room_id` int NULL DEFAULT NULL COMMENT '房间id',
  `photo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '照片(存链接按逗号分隔)',
  `realname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `age` int NULL DEFAULT NULL COMMENT '年龄',
  `mobile_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `sex` tinyint NULL DEFAULT NULL COMMENT '性别(1男2女;0未知)',
  `id_card` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证',
  `birthday` date NULL DEFAULT NULL COMMENT '生日如1988-10-23',
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '省',
  `town` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '市',
  `area` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区',
  `community` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '社区()',
  `domicile_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '户籍地址',
  `detailed_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '现居详细地址',
  `nation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '民族',
  `education_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文化程度',
  `marital_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '婚姻状况',
  `emgency_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '紧急联系人姓名',
  `emgency_sex` tinyint NULL DEFAULT NULL COMMENT '紧急联系人性别',
  `emgency_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '紧急联系人电话',
  `relation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关系',
  `work_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工作单位',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '注销时间',
  `del` int NULL DEFAULT NULL COMMENT '删除(0不删除1已删除)一般为0能用1不能用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '老人信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_message_notice
-- ----------------------------
DROP TABLE IF EXISTS `elder_message_notice`;
CREATE TABLE `elder_message_notice`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `elder_id` bigint NULL DEFAULT NULL COMMENT '接收人ID',
  `billing_id` bigint NULL DEFAULT NULL COMMENT '账单ID',
  `room_id` bigint NULL DEFAULT NULL COMMENT 'roomID',
  `notification_method` tinyint NULL DEFAULT NULL COMMENT '通知方式：1-短信，2-微信，3-邮件',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-待发送，2-已发送，3-发送失败',
  `send_time` datetime NULL DEFAULT NULL COMMENT '发送时间',
  `mssage_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '消息类型: 1-缴费类;2-活动折扣类3-菜单类 4-交接班类',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '缴费通知表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_reservation
-- ----------------------------
DROP TABLE IF EXISTS `elder_reservation`;
CREATE TABLE `elder_reservation`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '预订ID',
  `elder_id` bigint NULL DEFAULT NULL COMMENT '长者ID',
  `contact_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `room_id` bigint NULL DEFAULT NULL COMMENT '房间ID',
  `expected_check_in_date` datetime NULL DEFAULT NULL COMMENT '预计入住日期',
  `reservation_date` datetime NULL DEFAULT NULL COMMENT '预订日期',
  `deposit` decimal(10, 2) NULL DEFAULT NULL COMMENT '押金',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0-已取消，1-已预订，2-已转入住',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `del` tinyint NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_elder_id`(`elder_id` ASC) USING BTREE,
  INDEX `idx_reservation_date`(`reservation_date` ASC) USING BTREE,
  INDEX `idx_expected_check_in_date`(`expected_check_in_date` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '预订记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_room
-- ----------------------------
DROP TABLE IF EXISTS `elder_room`;
CREATE TABLE `elder_room`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `elder_id` bigint NOT NULL COMMENT '老人id',
  `room_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '房间编号',
  `building` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '楼栋',
  `floor` int NULL DEFAULT NULL COMMENT '楼层',
  `room_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '房间类型',
  `bed_count` int NULL DEFAULT 0 COMMENT '床位数量',
  `bed_number` int NULL DEFAULT NULL COMMENT '床位号',
  `facilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '设施设备',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0-维修中，1-可用，2-已满',
  `room_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '房间费用',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `del` tinyint NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '房间信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_service_living
-- ----------------------------
DROP TABLE IF EXISTS `elder_service_living`;
CREATE TABLE `elder_service_living`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `elder_id` bigint NULL DEFAULT NULL COMMENT 'elder_id',
  `elder_bed_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '老人床位号',
  `elder_room_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '老人房间号',
  `accident_report` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事故报告',
  `visit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '探访人的名字',
  `visit_person` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '探访人的名字',
  `visit_purpose` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '探访人的事由',
  `visit_start_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '探访开始时间',
  `visit_end_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '探访结束时间',
  `leave_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请假申请内容',
  `leave_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请假原因',
  `leave_start_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '请假开始时间',
  `leave_end_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '请假结束时间',
  `incident_time` datetime NULL DEFAULT NULL COMMENT '事故发生时间',
  `incident_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事故地点',
  `incident_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '事故描述',
  `incident_severity` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '严重程度：minor, moderate, severe',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_elder_id`(`elder_id` ASC) USING BTREE,
  INDEX `idx_elder_bed_number`(`elder_bed_number` ASC) USING BTREE,
  INDEX `idx_elder_room_number`(`elder_room_number` ASC) USING BTREE,
  INDEX `idx_visit_start_time`(`visit_start_time` ASC) USING BTREE,
  INDEX `idx_leave_start_time`(`leave_start_time` ASC) USING BTREE,
  INDEX `idx_incident_time`(`incident_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '长者生活服务记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for elder_service_nursing
-- ----------------------------
DROP TABLE IF EXISTS `elder_service_nursing`;
CREATE TABLE `elder_service_nursing`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `record_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '类型：1护理计划), 2护理项目), 3(护理记录), 4(护理变更), 5(核查), 6生活记录)',
  `care_plan` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '护理计划内容',
  `care_item` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '护理项目名称',
  `care_record` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '护理记录详情',
  `care_batch` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '护理班次',
  `care_level` int NULL DEFAULT NULL COMMENT '护理等级',
  `care_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '护理费用',
  `change_person_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '护理变更人姓名',
  `audit_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending' COMMENT '核查状态：1(待核查), 2(已通过), 3(未通过), 4(核查中)',
  `life_record` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '生活记录内容',
  `caregiver_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '护理人姓名',
  `caregiver_post` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '护理人岗位',
  `caregiver_group` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '护理小组名称',
  `bed_number` int NULL DEFAULT NULL COMMENT '床位号',
  `care_knowledge` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '护理知识库',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_record_type`(`record_type` ASC) USING BTREE,
  INDEX `idx_audit_status`(`audit_status` ASC) USING BTREE,
  INDEX `idx_caregiver_name`(`caregiver_name` ASC) USING BTREE,
  INDEX `idx_bed_number`(`bed_number` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '护理服务管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for elder_service_plan
-- ----------------------------
DROP TABLE IF EXISTS `elder_service_plan`;
CREATE TABLE `elder_service_plan`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '护理计划ID',
  `elder_id` bigint NULL DEFAULT NULL COMMENT '长者ID',
  `nurse_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '护工ID',
  `plan_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计划内容(换药,打点滴,散步)',
  `start_date` datetime NULL DEFAULT NULL COMMENT '开始日期',
  `end_date` datetime NULL DEFAULT NULL COMMENT '结束日期',
  `care_level` int NULL DEFAULT NULL COMMENT '护理等级',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0-已终止，1-执行中，2-已完成',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '护理计划表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for scheduling_template
-- ----------------------------
DROP TABLE IF EXISTS `scheduling_template`;
CREATE TABLE `scheduling_template`  (
  `id` bigint NOT NULL COMMENT 'id',
  `shift_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '班次(白/晚班)',
  `nurse_give_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '交班人员',
  `nurse_take_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接班人员',
  `is_guard` tinyint NULL DEFAULT 1 COMMENT '是否巡更',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for survey_consultation
-- ----------------------------
DROP TABLE IF EXISTS `survey_consultation`;
CREATE TABLE `survey_consultation`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '咨询记录ID',
  `elder_id` bigint NULL DEFAULT NULL COMMENT '长者即用户app_user_id',
  `consultant_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '咨询人姓名',
  `consultant_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '咨询人电话',
  `purpose` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '咨询目的',
  `expected_check_in_date` datetime NULL DEFAULT NULL COMMENT '预计入住日期',
  `channel_type` tinyint NULL DEFAULT NULL COMMENT '媒介渠道:1朋友推荐2线下3网络',
  `status` tinyint NULL DEFAULT 0 COMMENT '状态：0-新咨询，1-跟进中，2-已签约，3-已关闭，4-已失效',
  `close_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关闭/失效原因',
  `conversion_date` datetime NULL DEFAULT NULL COMMENT '签约日期',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `file_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '真实图片',
  `attachment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件路径',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `del` tinyint NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '咨询接待记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_dict_properties
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_properties`;
CREATE TABLE `sys_dict_properties`  (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 240 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 149 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '模块标题',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '方法名称',
  `method_remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '方法中文名称/备注',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '请求方式',
  `sys_type` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '主机地址',
  `req_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '请求参数',
  `res_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '返回参数',
  `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint NULL DEFAULT 0 COMMENT '消耗时间（毫秒）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_log_title`(`title` ASC) USING BTREE,
  INDEX `idx_sys_log_oper_name`(`oper_name` ASC) USING BTREE,
  INDEX `idx_sys_log_oper_time`(`oper_time` ASC) USING BTREE,
  INDEX `idx_sys_log_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_mgr_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_mgr_user`;
CREATE TABLE `sys_mgr_user`  (
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `mobile_phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '用户性别（1男2女 0未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '密码',
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '省',
  `town` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '市',
  `area` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区',
  `account_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父权限ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限名称',
  `permission_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `type` tinyint NULL DEFAULT NULL COMMENT '类型：0-目录，1-菜单，2-按钮',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` tinyint(1) NULL DEFAULT 1 COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` tinyint(1) NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 109 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_role_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_permission`;
CREATE TABLE `sys_role_permission`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_role_id`(`role_id` ASC) USING BTREE,
  INDEX `idx_permission_id`(`permission_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色权限关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_stock
-- ----------------------------
DROP TABLE IF EXISTS `sys_stock`;
CREATE TABLE `sys_stock`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `item_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物品名字',
  `item_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物品序列号',
  `batch_number` bigint NOT NULL COMMENT '批次号',
  `barcode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '条形码',
  `stock_in_reason` tinyint NULL DEFAULT NULL COMMENT '1采购入库;2调拨入库;3退货入库',
  `stock_out_reason` tinyint NULL DEFAULT NULL COMMENT '1领用出库2调拨出库 3退货出库 4报废出库, 5其他出库',
  `quantity` int NOT NULL DEFAULT 0 COMMENT '库存数量',
  `recipient_department` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '领用部门',
  `recipient_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '领用人',
  `unit_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '单价',
  `last_check_date` date NULL DEFAULT NULL COMMENT '最近盘点日期',
  `stock_state` tinyint NOT NULL COMMENT '1.入库待审核2-审核通过已入库;3-出库待审核4-审核通过已出库',
  `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '供应商',
  `supplier_mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '供应商电话',
  `supplier_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '供应商地址',
  `warehouse` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '仓库',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `stock_in_date` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `stock_out_date` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '出库时间',
  `del` tinyint NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '库存表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_role_id`(`role_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户角色关联表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
