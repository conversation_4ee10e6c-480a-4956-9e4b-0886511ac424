apiVersion: apps/v1
kind: Deployment
metadata:
  name: cityserver-chpg-deployment
  labels:
    platform: city
    kind: chpg
spec:
  # replicas: 2
  selector:
    matchLabels:
      app: spring-chpg
  template:
    metadata:
      labels:
        app: spring-chpg
    spec:
      terminationGracePeriodSeconds: 12
      imagePullSecrets:
        - name: regcred
      hostAliases:
        - ip: "*************"
          hostnames:
            - "www.liquibase.org"
      containers:
        - name: cityserver
          image: registry.cn-shanghai.aliyuncs.com/czmp/czmpcityserver-chpg
          imagePullPolicy: "IfNotPresent"
          resources:
            limits:
              memory: 8G
              cpu: 4
          ports:
            - containerPort: 8080
          envFrom:
            - configMapRef:
                name: cityserver-chpg-envs
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: prod,no-liquibase
          # https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
          readinessProbe:
            httpGet:
              path: /management/health
              port: 8080
            initialDelaySeconds: 6
            periodSeconds: 3
