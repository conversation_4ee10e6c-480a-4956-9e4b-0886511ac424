# ap elastic/fetch_template -e="host=city_elastic"
# ap elastic/fetch_template -e="host=jintan_elastic"
# ap elastic/fetch_template -e="host=liyang_elastic"
# ap elastic/fetch_template -e="host=wujin"
# ap elastic/fetch_template -e="host=xinbei"
# ap elastic/fetch_template -e="host=points_taizhou"
# ap elastic/fetch_template -e="host=city_elastic,jintan_elastic,liyang_elastic,wujin,xinbei,points_taizhou"

- name: Fetch Template
  hosts: "{{ host }}"
  vars_files:
    - common_vars.yaml
  
  tasks:
    - include_tasks: common_tasks.yaml

    - name: curl _template
      shell: "curl -u elastic:{{ pwd }} -X GET 'http://127.0.0.1:9200/_template/*_template'"
      register: result

    - name: Save _template
      copy:
        content: "{{ result.stdout | from_json | to_nice_json }}"
        dest: /tmp/es_template.json

    - name: fetch _template
      fetch:
        src: /tmp/es_template.json
        dest: ~/work/fetch_data/es_templates/{{ inventory_hostname }}.json
        flat: true