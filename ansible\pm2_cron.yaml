# ap pm2_cron --extra-vars="host=p27 app=hik-mono"
# ap pm2_restart --extra-vars="host=p27 app=hik-mono"

# ap pm2_cron --extra-vars="host=points_jintan app=nova-driver"
# ap pm2_restart --extra-vars="host=points_jintan app=nova-driver"

- name: pm2 cron
  hosts: "{{ host }}"
  tasks:
    - ansible.builtin.cron:
        name: pm2 restart
        minute: "0"
        hour: "19"
        job: "pm2 restart {{ app }}"
