tunnel: f280cca0-895a-4d4c-912e-9aa8df3c5ca3
credentials-file: /home/<USER>/.cloudflared/f280cca0-895a-4d4c-912e-9aa8df3c5ca3.json
protocol: auto
ingress:
  - hostname: y5-ssh.socode.top
    service: ssh://localhost:1122
  - hostname: y5-api.socode.top
    service: http://localhost:8080
  - hostname: y5-pg.socode.top
    service: tcp://localhost:5432
  - hostname: y5-clickhouse.socode.top
    service: http://localhost:8123
  - hostname: y5-cockroach.socode.top
    service: tcp://localhost:26257
  - hostname: y5-cockroach-web.socode.top
    service: http://localhost:8082
  - service: http_status:404

# cloudflared tunnel route dns y5 y5-ssh.socode.top
# cloudflared tunnel route dns y5 y5-api.socode.top
# cloudflared tunnel route dns y5 y5-pg.socode.top
# cloudflared tunnel route dns y5 y5-clickhouse.socode.top
# cloudflared tunnel route dns y5 y5-cockroach.socode.top
# cloudflared tunnel route dns y5 y5-cockroach-web.socode.top