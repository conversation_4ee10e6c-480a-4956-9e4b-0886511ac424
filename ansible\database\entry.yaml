# ap database/entry -t points --extra-vars="play=dicts"
# ap database/entry -t area --extra-vars="play=dicts"
# ap database/entry -t city --extra-vars="play=sql"
# ap database/entry -t area --extra-vars="play=sql"
# ap database/entry -t liyang --extra-vars="play=sql"
# ap database/entry -t points --extra-vars="play=sql"

- name: Office
  import_playbook: "{{ play }}.yaml"
  tags: [office]
  vars:
    inventory: office
    login_password: CZMP.1019
    db: CzmpCityServer

- name: City
  import_playbook: "{{ play }}.yaml"
  tags: [city]
  vars:
    inventory: y4,y2
    login_password: CZMP^2023
    db: czmpcity

- name: Jintan
  import_playbook: "{{ play }}.yaml"
  tags: [area,jintan]
  vars:
    inventory: jintan_pg
    db: czmp-area
    login_password: CZMP.1019

- name: Liyang
  import_playbook: "{{ play }}.yaml"
  tags: [area,liyang]
  vars:
    inventory: liyang_pg
    db: czmp-area
    login_password: CZMP.1019
    pg_port: 5432

- name: Wujin
  import_playbook: "{{ play }}.yaml"
  tags: [area,wujin]
  vars:
    inventory: wujin
    db: czmp-area
    login_password: CZMP.1019

- name: Xinbei
  import_playbook: "{{ play }}.yaml"
  tags: [area,xinbei]
  vars:
    inventory: xinbei
    db: czmp-area
    login_password: CZMP.1019
    pg_port: 14003

- name: Points
  import_playbook: "{{ play }}.yaml"
  tags: [points]
  vars:
    inventory: points
    db: czmppoint
    login_password: CZMP.2019
    pg_port: 5432
