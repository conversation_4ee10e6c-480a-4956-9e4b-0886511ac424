基于这份 SQL DDL 文件生成一个 markdown 格式的数据结构说明文档。只需要中文信息，不需要英文字段名。
每个字段单独一行，要包含字段类型（数组、字符串等），如果有外键关系，需要在当前行描述。
不要另起一个“外键关系”主题。不要写跟数据结构无关的事情，比如“业务流程”。


`.kiro/specs/nursing-home-management/design.md` 是项目设计规划文档。
`.kiro/specs/nursing-home-management/tasks.md` 是基于设计规划文档拆解出来的任务列表。当前开发进度已完成到 7.4。
现在需要继续往下执行开发任务。

## 技术栈
- nodejs/typescript/pnpm/Next.js
- PostgreSQL/supabase/drizzle/better-auth
- tailwindcss/shadcn/ECharts/framer-motion
- zod/zustand/react-hook-form

## 其它要求
- 保持与现有代码风格和架构一致
- 使用现有的组件库和工具函数
- 遵循项目的文件结构和命名规范
- 确保类型安全和错误处理