tunnel: 18f19cb2-5444-43ae-9a6b-d224f4057db9
credentials-file: /home/<USER>/.cloudflared/18f19cb2-5444-43ae-9a6b-d224f4057db9.json
protocol: auto
ingress:
  - hostname: p12-ssh.socode.top
    service: ssh://localhost:22
  - hostname: p12-api.socode.top
    service: http://localhost:80
  - hostname: p12-hub.socode.top
    service: http://localhost:9133
  - hostname: p12-kibana.socode.top
    service: http://localhost:5601
  - hostname: p12-kafka-87.socode.top
    service: http://localhost:8087
  - hostname: p12-pg.socode.top
    service: tcp://localhost:5432
  - hostname: p12-clickhouse.socode.top
    service: http://localhost:8123
  - hostname: p12-minio.socode.top
    service: http://localhost:9000
  - hostname: p12-minio-console.socode.top
    service: http://localhost:9001
  - hostname: p12-grafana.socode.top
    service: http://localhost:3080
  - hostname: p12-web.socode.top
    service: http://localhost:16002
  - service: http_status:404

# cloudflared tunnel route dns p12 p12-ssh.socode.top
# cloudflared tunnel route dns p12 p12-api.socode.top
# cloudflared tunnel route dns p12 p12-hub.socode.top
# cloudflared tunnel route dns p12 p12-web.socode.top
# cloudflared tunnel route dns p12 p12-kibana.socode.top
# cloudflared tunnel route dns p12 p12-kafka-87.socode.top
# cloudflared tunnel route dns p12 p12-pg.socode.top
# cloudflared tunnel route dns p12 p12-clickhouse.socode.top
# cloudflared tunnel route dns p12 p12-minio.socode.top
# cloudflared tunnel route dns p12 p12-minio-console.socode.top
# cloudflared tunnel route dns p12 p12-grafana.socode.top