CREATE TABLE cityserver.bs_dd_check_weight (
  `check_no` String,
  `site_id` Nullable(String),
  `company_id` Nullable(String),
  `lane_no` Nullable(String),
  `lane_code` Nullable(String),
  `check_type` Nullable(Int32),
  `reverse_tf` Nullable(Int32),
  `is_preliminary` Nullable(UInt8),
  `check_time` DateTime64(6),
  `create_time` Nullable(DateTime64(6)),
  `camera_device_time` Nullable(DateTime64(6)),
  `weight_device_time` Nullable(DateTime64(6)),
  `obu_device_time` Nullable(DateTime64(6)),
  `video_upload_time` Nullable(DateTime64(6)),
  `link_check_no` Nullable(String),
  `car_type` Nullable(Int32),
  `cam_car_type` Nullable(Int32),
  `vehicle_no` String,
  `hang_vehicle_no` Nullable(String),
  `hang_plate_color` Nullable(Int32),
  `plate_type` Nullable(Int32),
  `cam_plate_type` Nullable(Int32),
  `plate_color` Nullable(Int32),
  `cam_plate_color` Nullable(Int32),
  `cam_car_brand` Nullable(Int32),
  `cam_car_color` Nullable(Int32),
  `axles` Nullable(Int32),
  `axles_type` Nullable(String),
  `original_axles` Nullable(Int32),
  `original_axles_type` Nullable(String),
  `total` Nullable(Int32),
  `axles_struct` Nullable(String),
  `axles_groups` Nullable(Int32),
  `axles_groups_weight_struct` Nullable(String),
  `weight_1` Nullable(Int32),
  `weight_2` Nullable(Int32),
  `weight_3` Nullable(Int32),
  `weight_4` Nullable(Int32),
  `weight_5` Nullable(Int32),
  `weight_6` Nullable(Int32),
  `weight_7` Nullable(Int32),
  `weight_8` Nullable(Int32),
  `speed` Nullable(Int32),
  `limit_weight` Nullable(Int32),
  `over_weight` Nullable(Int32),
  `over_rate` Nullable(Decimal128(2)),
  `distance_max` Nullable(Int32),
  `distance_struct` Nullable(String),
  `bulk_vehicle_tf` Nullable(Int32),
  `driving_direction` Nullable(String),
  `obu_id` Nullable(String),
  `obu_vehicle_no` Nullable(String),
  `obu_plate_color` Nullable(Int32),
  `weight_device_no` Nullable(String),
  `temperature` Nullable(String),
  `data_source` Nullable(Int32),
  `transport_license_id` Nullable(String),
  `large_transport_code` Nullable(String),
  `audit_state` Nullable(Int32),
  `city_aduit_status` Nullable(String),
  `escape_state` Nullable(Int32),
  `delete` Nullable(Int32),
  `delete_reson` Nullable(String),
  `illegal_code` Nullable(Int32),
  `missing_media_11` Nullable(UInt8),
  `missing_media_12` Nullable(UInt8),
  `missing_media_13` Nullable(UInt8),
  `missing_media_14` Nullable(UInt8),
  `missing_media_15` Nullable(UInt8),
  `missing_media_110` Nullable(UInt8),
  `missing_media_31` Nullable(UInt8),
  `is_unusual` Nullable(UInt8),
  `overload_process_status` Nullable(String),
  `data_type` Nullable(String),
  `is_copy_report` Nullable(UInt8),
  `is_absolve` Nullable(UInt8),
  `is_manual` Nullable(UInt8),
  `total_length` Nullable(Int32),
  `total_width` Nullable(Int32),
  `total_height` Nullable(Int32),
  `over_length` Nullable(Int32),
  `over_width` Nullable(Int32),
  `over_height` Nullable(Int32),
  `std_length` Nullable(Int32),
  `std_width` Nullable(Int32),
  `std_height` Nullable(Int32),
  `is_send_msg` Nullable(UInt8),
  `is_case_lib` Nullable(UInt8),
  `audit_process_type` Nullable(String),
  `is_letter` Nullable(UInt8),
  `is_msg_notice_letter` Nullable(UInt8),
  `is_paper_notice_letter` Nullable(UInt8),
  `union_enforcement` Nullable(String),
  `traffic_enforcement` Nullable(String),
  `is_edocument` Nullable(UInt8),
  `cam_tail_car_type` Nullable(Int32),
  `missing_media_21` Nullable(UInt8),
  `officer_id` Nullable(Int64),
  `update_time` Nullable(DateTime64(6)),
  `province` Nullable(Int32),
  `city_symbol` Nullable(String),
  `is_punish` Nullable(UInt8),
  `has_danger_goods` Nullable(UInt8)
) ENGINE = MergeTree() PRIMARY KEY (check_no)
ORDER BY tuple(check_no, check_time)