tunnel: c750d69b-6d60-4789-8c6b-410fb6972907
credentials-file: /home/<USER>/.cloudflared/c750d69b-6d60-4789-8c6b-410fb6972907.json
protocol: auto
ingress:
  - hostname: p15-ssh.socode.top
    service: ssh://localhost:22
  - hostname: p15-api.socode.top
    service: http://localhost:80
  - hostname: p15-hub.socode.top
    service: http://localhost:9133
  - hostname: p15-kibana.socode.top
    service: http://localhost:5601
  - hostname: p15-kafka-87.socode.top
    service: http://localhost:8087
  - hostname: p15-pg.socode.top
    service: tcp://localhost:5432
  - hostname: p15-clickhouse.socode.top
    service: http://localhost:8123
  - hostname: p15-minio.socode.top
    service: http://localhost:9000
  - hostname: p15-minio-console.socode.top
    service: http://localhost:9001
  - hostname: p15-grafana.socode.top
    service: http://localhost:3080
  - hostname: p15-web.socode.top
    service: http://localhost:16002
  - service: http_status:404

# cloudflared tunnel route dns p15 p15-ssh.socode.top
# cloudflared tunnel route dns p15 p15-api.socode.top
# cloudflared tunnel route dns p15 p15-hub.socode.top
# cloudflared tunnel route dns p15 p15-web.socode.top
# cloudflared tunnel route dns p15 p15-kibana.socode.top
# cloudflared tunnel route dns p15 p15-kafka-87.socode.top
# cloudflared tunnel route dns p15 p15-pg.socode.top
# cloudflared tunnel route dns p15 p15-clickhouse.socode.top
# cloudflared tunnel route dns p15 p15-minio.socode.top
# cloudflared tunnel route dns p15 p15-minio-console.socode.top
# cloudflared tunnel route dns p15 p15-grafana.socode.top