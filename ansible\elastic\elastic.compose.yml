version: "3.8"

# https://github.com/deviantony/docker-elk

services:
  elastic:
    image: "elasticsearch:${ELK_VERSION:-7.17.5}" # docker.elastic.co/* 没有国内加速
    container_name: elastic
    restart: on-failure
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      ES_JAVA_OPTS: $ES_JAVA_OPTS
      # bootstrap.memory_lock: "true" # block swap
      # https://www.elastic.co/guide/en/elasticsearch/reference/current/docker.html#_prepare_the_environment
      ELASTIC_PASSWORD: $ELASTIC_PASSWORD

      cluster.name: "docker-cluster"
      cluster.max_shards_per_node: 5000
      node.name: elastic
      network.publish_host: $NETWORK_PUBLISH_HOST
      network.host: 0.0.0.0

      # https://www.elastic.co/guide/en/elasticsearch/reference/current/behavioral-analytics-cors.html
      http.cors.allow-origin: "*" # fail for version 9
      http.cors.enabled: "true"
      http.cors.allow-credentials: "true"
      http.cors.allow-methods: OPTIONS, POST, GET
      http.cors.allow-headers: X-Requested-With, X-Auth-Token, Content-Type, Content-Length, Authorization, Access-Control-Allow-Headers, Accept

      # Use single node discovery in order to disable production mode and avoid bootstrap checks.
      # see: https://www.elastic.co/guide/en/elasticsearch/reference/current/bootstrap-checks.html
      discovery.type: $DISCOVERY_TYPE
      ${INITIAL_MASTER_NODES_ENVKEY:-/dev/null}: ${INITIAL_MASTER_NODES:-/dev/null}
      discovery.seed_hosts: ${SEED_HOSTS:-/dev/null}
      
      xpack.license.self_generated.type: basic
      xpack.security.enabled: "true" # disable for cluster if cannot use SSL, and fail for version 9
      xpack.monitoring.collection.enabled: "false"
    volumes:
      - type: bind
        source: $ES_DATA
        target: /usr/share/elasticsearch/data
      - type: volume
        source: esplugin
        target: /usr/share/elasticsearch/plugins
    # cpus: 2.50
    # ulimits: # 防止 daemon 的 ulimit 配置
    #   memlock:
    #     soft: -1
    #     hard: -1

volumes:
  esplugin:
    driver: local

