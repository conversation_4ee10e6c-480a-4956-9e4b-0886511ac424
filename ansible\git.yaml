# ap -t update git

- name: update workspace
  hosts: all_hosts
  tags: update
  tasks:
    - name: safe.directory
      shell: git config --global --add safe.directory ~/operation
    - name: checkout clean
      shell:
        chdir: ~/operation
        cmd: git checkout -- .
    - name: set origin
      shell:
        chdir: ~/operation
        cmd: git remote set-<NAME_EMAIL>:zicjin/operation.git
    - name: pull
      shell:
        chdir: ~/operation
        cmd: git pull