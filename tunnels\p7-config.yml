tunnel: 4a080585-322e-4ba3-9a84-b4c8499b6a2a
credentials-file: /home/<USER>/.cloudflared/4a080585-322e-4ba3-9a84-b4c8499b6a2a.json
protocol: auto
ingress:
  - hostname: p7-ssh.socode.top
    service: ssh://localhost:22
  - hostname: p7-api.socode.top
    service: http://localhost:80
  - hostname: p7-hub.socode.top
    service: http://localhost:9133
  - hostname: p7-kibana.socode.top
    service: http://localhost:5601
  - hostname: p7-kafka-87.socode.top
    service: http://localhost:8087
  - hostname: p7-pg.socode.top
    service: tcp://localhost:5432
  - hostname: p7-clickhouse.socode.top
    service: http://localhost:8123
  - hostname: p7-minio.socode.top
    service: http://localhost:9000
  - hostname: p7-minio-console.socode.top
    service: http://localhost:9001
  - hostname: p7-grafana.socode.top
    service: http://localhost:3080
  - hostname: p7-web.socode.top
    service: http://localhost:16002
  - hostname: p7-camera.socode.top
    service: http://*************
  - service: http_status:404

# cloudflared tunnel route dns p7 p7-ssh.socode.top
# cloudflared tunnel route dns p7 p7-api.socode.top
# cloudflared tunnel route dns p7 p7-hub.socode.top
# cloudflared tunnel route dns p7 p7-web.socode.top
# cloudflared tunnel route dns p7 p7-kibana.socode.top
# cloudflared tunnel route dns p7 p7-kafka-87.socode.top
# cloudflared tunnel route dns p7 p7-pg.socode.top
# cloudflared tunnel route dns p7 p7-clickhouse.socode.top
# cloudflared tunnel route dns p7 p7-minio.socode.top
# cloudflared tunnel route dns p7 p7-minio-console.socode.top
# cloudflared tunnel route dns p7 p7-grafana.socode.top