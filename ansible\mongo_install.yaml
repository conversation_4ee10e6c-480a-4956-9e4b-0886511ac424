# ap mongo_install -K -e="host=points_jintan,points_wujin"
# https://devpress.csdn.net/cicd/62ec614219c509286f416e2c.html

- name: Install Mongodb
  hosts: "{{ host }}"
  become: true
  tasks:
    - name: Apt update
      apt:
        name: apt
        state: latest
        update_cache: yes

    - name: Import public key
      apt_key:
        url: 'https://pgp.mongodb.com/server-6.0.asc'
        state: present

    - name: Add repository
      apt_repository:
        filename: '/etc/apt/sources.list.d/mongodb-org-6.0.list'
        repo: 'deb https://repo.mongodb.org/apt/ubuntu {{ ansible_distribution_release }}/mongodb-org/6.0 multiverse'
        state: present
        update_cache: yes

    - name: Install mongodb
      apt:
        name: mongodb-org
        state: present
        update_cache: yes

    - name: Ensure mongodb is running and and enabled to start automatically on reboots
      service:
        name: mongod
        enabled: yes
        state: started

    # https://github.com/Ilyes512/ansible-role-mongodb/blob/master/tasks/main.yml#L43
    - name: Set mongod.conf
      lineinfile:
        dest: /etc/mongod.conf
        regexp: "{{ item.regex }}"
        line: "{{ item.line }}"
        state: present
      loop:
        - { regex: "^\\s*bindIp.*", line: "  bindIp: 0.0.0.0" }
        # - { regex: "^(#)?security:", line: "security:\n    authorization: enabled" } # 无法准确实现，需要手工修改
      notify: restart mongod
  
  handlers:
    - name: restart mongod
      shell: sudo systemctl restart mongod

# 不要用 mongodb_user, 无法照顾所有细节，胶水区太多
# https://docs.ansible.com/ansible/latest/collections/community/mongodb/mongodb_user_module.html