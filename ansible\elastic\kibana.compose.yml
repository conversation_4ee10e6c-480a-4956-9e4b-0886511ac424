version: "3.3"

services:
  kibana:
    image: "kibana:${ELK_VERSION:-7.17.5}"
    container_name: kibana
    restart: on-failure
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - "${KIBANA_PORT:-5601}:5601"
    environment:
      # https://www.elastic.co/guide/en/kibana/current/docker.html#environment-variable-config
      SERVER_NAME: kibana
      ELASTICSEARCH_HOSTS: '["http://${ELASTIC_HOSTS:-host.docker.internal:9200}"]'
      ELASTICSEARCH_USERNAME: elastic
      ELASTICSEARCH_PASSWORD: $ELASTIC_PASSWORD
      I18N_LOCALE: "zh-CN"
      XPACK_REPORTING_ROLES_ENABLED: "false"
      SERVER_PUBLICBASEURL: "http://$NETWORK_PUBLISH_HOST:${KIBANA_PORT:-5601}"
