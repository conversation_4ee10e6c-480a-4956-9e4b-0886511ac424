\c czmpcity

-- This will automatically propogate the publication to all worker nodes.
CREATE PUBLICATION cdc_pub_checks FOR TABLE bs_dd_check_weight;
CREATE PUBLICATION cdc_pub_vehicle FOR TABLE bs_dd_vehicle_info;
CREATE PUBLICATION cdc_pub_log FOR TABLE operation_log;

\dRp
DROP PUBLICATION IF EXISTS cityserver_mater_pg_ch_publication;
SELECT * FROM pg_publication_tables;

-- create a logical replication slot on all citus nodes.
SELECT * FROM run_command_on_all_nodes
      ($$SELECT pg_create_logical_replication_slot('cdc_slot_checks', 'pgoutput', false);$$);
SELECT * FROM run_command_on_all_nodes
      ($$SELECT pg_create_logical_replication_slot('cdc_slot_vehicle', 'pgoutput', false);$$);
SELECT * FROM run_command_on_all_nodes
      ($$SELECT pg_create_logical_replication_slot('cdc_slot_log', 'pgoutput', false);$$);


SELECT * FROM pg_replication_slots;
select pg_drop_replication_slot('cityserver_mater_pg_ch_replication_slot');


-- CREATE PUBLICATION authpub FOR TABLE jhi_user, jhi_authority, jhi_user_authority, role, officer, bs_sys_site, bs_sys_org;
-- CREATE PUBLICATION vehicle_info FOR TABLE bs_dd_vehicle_info;
-- ALTER PUBLICATION authpub ADD TABLE bs_sys_site, bs_sys_org