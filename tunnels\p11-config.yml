tunnel: 31905629-e025-4fca-96e6-65dc169a88b8
credentials-file: /home/<USER>/.cloudflared/31905629-e025-4fca-96e6-65dc169a88b8.json
protocol: auto
ingress:
  - hostname: p11-ssh.socode.top
    service: ssh://localhost:22
  - hostname: p11-api.socode.top
    service: http://localhost:80
  - hostname: p11-kibana.socode.top
    service: http://localhost:5601
  - hostname: p11-kafka.socode.top
    service: http://localhost:8087
  - hostname: p11-pg.socode.top
    service: tcp://localhost:5432
  - hostname: p11-clickhouse.socode.top
    service: http://localhost:8123
  - hostname: p11-minio.socode.top
    service: http://localhost:9000
  - hostname: p11-minio-console.socode.top
    service: http://localhost:9001
  - hostname: p11-web.socode.top
    service: http://localhost:16002
  - service: http_status:404

# cloudflared tunnel route dns p11 p11-ssh.socode.top
# cloudflared tunnel route dns p11 p11-api.socode.top
# cloudflared tunnel route dns p11 p11-kibana.socode.top
# cloudflared tunnel route dns p11 p11-kafka.socode.top
# cloudflared tunnel route dns p11 p11-pg.socode.top
# cloudflared tunnel route dns p11 p11-clickhouse.socode.top
# cloudflared tunnel route dns p11 p11-minio.socode.top
# cloudflared tunnel route dns p11 p11-minio-console.socode.top
# cloudflared tunnel route dns p11 p11-web.socode.top