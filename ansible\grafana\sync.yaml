# ansible-galaxy collection install community.docker
# ansible-galaxy collection install ansible.posix

# root is necessary: https://docs.ansible.com/archive/ansible/2.3/synchronize_module.html#notes
# ansible-playbook -i inventory.yaml grafana/sync.yaml

- name: synchronization config
  hosts: grafana_nodes,grafana_master
  tasks:
    # https://docs.ansible.com/ansible/latest/collections/ansible/builtin/file_module.html#examples
    - name: standby directory
      file:
        path: /var/lib/ansible/grafana
        state: directory
        mode: '0755'
    - name: push env
      ansible.posix.synchronize:
        src: .env
        dest: /var/lib/ansible/grafana
    - name: push grafana9.ini
      ansible.posix.synchronize:
        src: grafana9.ini
        dest: /var/lib/ansible/grafana
    - name: push grafana.yml
      ansible.posix.synchronize:
        src: grafana.yml
        dest: /var/lib/ansible/grafana

- name: reset nodes
  hosts: grafana_nodes
  gather_facts: no
  tasks:
    - name: compose down
      # https://docs.ansible.com/ansible/2.9/modules/docker_compose_module.html
      community.docker.docker_compose: 
        project_src: /var/lib/ansible/grafana
        files: grafana.yml
        state: absent
      ignore_errors: true
    - name: rm grafana_data
      shell: "rm -rf /mnt/data/grafana_data"
      ignore_errors: true

- name: download grafana_data
  hosts: grafana_master
  gather_facts: no
  tasks:
    # https://docs.ansible.com/ansible/latest/collections/ansible/posix/synchronize_module.html
    - ansible.posix.synchronize:
        mode: pull
        src: /mnt/data/grafana_data
        dest: ~/Downloads/
        archive: true
        recursive: true
        delete: true

- name: synchronization grafana_data and start
  hosts: grafana_nodes
  gather_facts: no
  tasks:
    - name: upload grafana_data
      ansible.posix.synchronize:
        src: ~/Downloads/grafana_data
        dest: /mnt/data/
        archive: true
        recursive: true
        delete: true
    - name: start grafanas
      community.docker.docker_compose:
        project_src: /var/lib/ansible/grafana
        files: grafana.yml
        state: present
    - name: Pause to wait for grafana startup
      ansible.builtin.pause:
        seconds: "5"

- name: PUT alerting-flowraft
  hosts: grafana_nodes
  gather_facts: no
  tasks:
    - uri:
        url: http://localhost/api/alerting-flowraft?flowRaft=0
        user: admin
        password: "Cz.3202&"
        method: PUT
        force_basic_auth: true