# ansible-galaxy collection install ericsysmin.system
# ap chrony -K --extra-vars="host=cloud"

- name: chrony
  hosts: "{{ host }}"
  become: yes
  roles:
    - role: ericsysmin.system.chrony
      chrony_config_server:
        - ntp.aliyun.com
        - ntp1.aliyun.com
        - ntp2.aliyun.com
        - ntp3.aliyun.com
        - ntp4.aliyun.com
        - ntp5.aliyun.com
        - ntp6.aliyun.com
        - ntp7.aliyun.com

# https://stackoverflow.com/a/51864689


# # Specify one or more NTP servers.
# server ntp.aliyun.com prefer
# server ntp1.aliyun.com iburst
# server ntp2.aliyun.com iburst
# server ntp3.aliyun.com iburst
# server ntp4.aliyun.com iburst

# # Use Ubuntu's ntp server as a fallback.
# pool ntp.ubuntu.com

# /etc/systemd/timesyncd.conf
# NTP=ntp.aliyun.com
# FallbackNTP=ntp1.aliyun.com,ntp2.aliyun.com,ntp3.aliyun.com,ntp4.aliyun.com,ntp5.aliyun.com