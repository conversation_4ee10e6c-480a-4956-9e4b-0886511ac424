# ansible-playbook -i inventory.yaml database/move_shard_placement.yaml

- name: move_shard_placement
  hosts: y4
  gather_facts: no
  tasks:
    - name: SELECT shardid, nodename
      postgresql_query:
        login_host: 127.0.0.1
        db: czmpcity
        login_password: CZ<PERSON>^2023
        port: 5432
        query: SELECT shardid, nodename from citus_shards WHERE table_name = 'bs_dd_check_weight'::regclass AND nodename <> '*************';
      register: shard
    - name: shard_debug
      debug: msg="{{ shard.statusmessage }} | {{ shard.query_result }}"
    - name: citus_move_shard_placement
      postgresql_query:
        login_host: 127.0.0.1
        db: czmpcity
        login_password: CZMP^2023
        port: 5432
        query: SELECT citus_move_shard_placement('{{ item.shardid }}', '{{ item.nodename }}', 5432, '*************', 5432);
      register: result
      loop: "{{ shard.query_result }}"
      