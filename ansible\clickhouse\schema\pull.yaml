# ap clickhouse/schema/pull

- name: clickhouse
  hosts: y6
  gather_facts: no
  tasks:
    - shell: rm /home/<USER>/schema/*
      ignore_errors: yes

    - shell: >
        clickhouse-client --password CZMP.2022 --port 9030 -q 
        "SHOW CREATE TABLE cityserver_mater_pg.bs_dd_check_weight format CSV" > "/home/<USER>/schema/bs_dd_check_weight_mater.sql"

    # - shell: >
    #     clickhouse-client --password CZMP.2022 --port 9030 -q 
    #     "SHOW CREATE TABLE cityserver_mater_pg.bs_dd_vehicle_info format CSV" > "/home/<USER>/schema/bs_dd_vehicle_info_mater.sql"

    # - shell: >
    #     clickhouse-client --password CZMP.2022 --port 9030 -q 
    #     "SHOW CREATE TABLE cityserver_mater_pg.alerting format CSV" > "/home/<USER>/schema/alerting_mater.sql"

    - name: download
      ansible.posix.synchronize:
        mode: pull
        src: /home/<USER>/schema
        dest: /Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts/ansible/clickhouse/
        recursive: true