{"log": {"error": "/Users/<USER>/.V2rayU/v2ray-core.log", "loglevel": "warning", "access": "/Users/<USER>/.V2rayU/v2ray-core.log"}, "inbounds": [{"listen": "127.0.0.1", "port": "7875", "protocol": "socks", "settings": {"auth": "<PERSON><PERSON><PERSON>", "udp": false}}, {"listen": "127.0.0.1", "settings": {"timeout": 360}, "port": "7879", "protocol": "http"}], "outbounds": [{"tag": "proxy", "settings": {"vnext": [{"port": 433, "users": [{"security": "none", "level": 0, "id": "08ea6cfc-c76b-11ec-9d64-0242ac120003", "alterId": 0}], "address": "tokyo.windcss.com"}]}, "mux": {"enabled": false, "concurrency": 8}, "protocol": "vmess", "streamSettings": {"security": "tls", "network": "ws", "wsSettings": {"path": "", "headers": {"host": ""}}, "tlsSettings": {"allowInsecure": false, "fingerprint": "chrome", "serverName": ""}}}, {"tag": "direct", "settings": {"domainStrategy": "UseIP", "userLevel": 0}, "protocol": "freedom"}, {"protocol": "blackhole", "tag": "block", "settings": {"response": {"type": "none"}}}], "dns": {}, "routing": {"settings": {"rules": [], "domainStrategy": "AsIs"}}}