# ap database/entry -t city --extra-vars="play=fetch_dRs"
# ap database/entry -t area --extra-vars="play=fetch_dRs"
# ap database/entry -t points --extra-vars="play=fetch_dRs"

# ap database/entry -t city,area,points --extra-vars="play=fetch_dRs"

- name: Fetch dRs
  hosts: "{{ inventory }}"
  gather_facts: no
  tasks:
    - postgresql_query:
        login_host: 127.0.0.1
        db: "{{ db }}"
        login_password: "{{ login_password }}"
        port: "{{ pg_port | default(5432) | int }}"
        autocommit: yes # https://docs.ansible.com/ansible/2.9/modules/postgresql_query_module.html#parameters
        query: > # \set ECHO_HIDDEN on
          SELECT subname AS "Name"
          ,  pg_catalog.pg_get_userbyid(subowner) AS "Owner"
          ,  subenabled AS "Enabled"
          ,  subpublications AS "Publication"
          FROM pg_catalog.pg_subscription
          WHERE subdbid = (SELECT oid
                          FROM pg_catalog.pg_database
                          WHERE datname = pg_catalog.current_database()) ORDER BY 1;
      register: result

    - name: Save result
      ansible.builtin.copy:
        content: "{{ result.query_result | to_nice_json }}"
        dest: /tmp/pg_dRs.json

    - ansible.builtin.fetch:
        src: /tmp/pg_dRs.json
        dest: ~/work/fetch_data/pg_dRs/{{ inventory_hostname }}.json
        flat: true