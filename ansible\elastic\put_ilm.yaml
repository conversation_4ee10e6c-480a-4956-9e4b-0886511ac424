# ap elastic/put_ilm -e="host=city_elastic"
# ap elastic/put_ilm -e="host=jintan_elastic"
# ap elastic/put_ilm -e="host=liyang_elastic"
# ap elastic/put_ilm -e="host=wujin"
# ap elastic/put_ilm -e="host=xinbei"
# ap elastic/put_ilm -e="host=points_taizhou"
# ap elastic/put_ilm -e="host=p21,p22"
# ap elastic/put_ilm -e="host=city_elastic,jintan_elastic,liyang_elastic,wujin,xinbei,points_taizhou"

- name: Put ilm
  hosts: "{{ host }}"
  vars_files:
    - common_vars.yaml
  
  tasks:
    - include_tasks: common_tasks.yaml
  
    - name: put delete_policy_90d
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_ilm/policy/delete_policy_90d \
          -u elastic:{{ pwd }} \
          -H 'Content-Type: application/json' \
          -d '{ "policy": { "phases" : { "delete" : { "min_age" : "90d", "actions" : { "delete" : { "delete_searchable_snapshot" : true } } } } } }'
      register: result
    - name: Show results 90d
      debug: msg="{{ result.cmd }} {{ result.stdout }} {{ result.stderr }}"

    - name: put delete_policy_60d
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_ilm/policy/delete_policy_60d \
          -u elastic:{{ pwd }} \
          -H 'Content-Type: application/json' \
          -d '{ "policy": { "phases" : { "delete" : { "min_age" : "60d", "actions" : { "delete" : { "delete_searchable_snapshot" : true } } } } } }'
      register: result2
    - name: Show results 60d
      debug: msg="{{ result2.cmd }} {{ result2.stdout }} {{ result2.stderr }}"

    - name: put delete_policy_30d
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_ilm/policy/delete_policy_30d \
          -u elastic:{{ pwd }} \
          -H 'Content-Type: application/json' \
          -d '{ "policy": { "phases" : { "delete" : { "min_age" : "60d", "actions" : { "delete" : { "delete_searchable_snapshot" : true } } } } } }'
      register: result3
    - name: Show results 30d
      debug: msg="{{ result3.cmd }} {{ result3.stdout }} {{ result3.stderr }}"