# ap fonts -K --extra-vars="host=t1"
# sudo apt-get install xfonts-utils fontconfig

- name: setup fonts
  hosts: "{{ host }}"
  become: yes
  tasks:
    - name: apt xfonts-utils
      apt:
        name: xfonts-utils
        state: present
    - name: apt fontconfig
      apt:
        name: fontconfig
        state: present
    - copy:
        src: "{{ item }}"
        dest: /usr/share/fonts
        mode: "0755"
      with_fileglob:
        - ~/work/czmp/czmp-fonts/*
    - name: setup
      shell: |
        mkfontscale
        mkfontdir
        fc-cache -fv
