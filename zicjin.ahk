;https://stackoverflow.com/a/50085293
;Compile the script to *.exe with Compiler/Ahk2Exe.exe
;save to win+r: shell:startup

;Minimize
#m::WinMinimize, A

;Onenote font------------------------------------------------------------------------------------
#c::
Send !h
Send ff
Send, Fira Code{Enter}
Return

;Onenote font size------------------------------------------------------------------------------------
#g::
Send !h
Send fs
Send, 11{Enter}
Return

;Onenote line heigh/space------------------------------------------------------------------------------------
#h::
Send !h
Send pp
Send ^a0{Tab}
Send ^a0{Tab}
Send ^a0{Tab}
Send, {Enter}
Return

;Onenote format painter------------------------------------------------------------------------------------
#b::
Send !h
Send fp
Return

;切换窗口固定在最前-------------------------------------------------------------------------------------
#f::
WinGet, active_id, ID, A
WinSet, AlwaysOnTop, toggle, ahk_id %active_id%
Return

; google translate
!w::
Send ^c
IfWinExist, Google Translate
	WinActivate
  Sleep, 100
  Send !d
  Sleep, 100
  Send ^v
Return

;pick color------------------------------------------------------------------------------------------------
#o::
MouseGetPos, mouseX, mouseY
PixelGetColor, color, %mouseX%, %mouseY%, RGB
StringRight color,color,6
clipboard = %color%
Return

; switch between open Windows of the same type and same App (.exe)
; https://github.com/JuanmaMenendez/AutoHotkey-script-Open-Show-Apps/blob/master/AutoHotkey-script-Switch-Windows-same-App.ahk
ExtractAppTitle(FullTitle)
{	
	AppTitle := SubStr(FullTitle, InStr(FullTitle, " ", false, -1) + 1)
	Return AppTitle
}
; Alt + ` -  Activate NEXT Window of same type (title checking) of the current APP
!`::
WinGet, ActiveProcess, ProcessName, A
WinGet, OpenWindowsAmount, Count, ahk_exe %ActiveProcess%

If OpenWindowsAmount = 1  ; If only one Window exist, do nothing
    Return

Else
	{
		WinGetTitle, FullTitle, A
		AppTitle := ExtractAppTitle(FullTitle)

		SetTitleMatchMode, 2		
		WinGet, WindowsWithSameTitleList, List, %AppTitle%
		
		If WindowsWithSameTitleList > 1 ; If several Window of same type (title checking) exist
		{
			WinActivate, % "ahk_id " WindowsWithSameTitleList%WindowsWithSameTitleList%	; Activate next Window	
		}
	}
Return
