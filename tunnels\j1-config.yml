tunnel: 8fe1b450-eb62-4379-b569-dd9c57cba47c
credentials-file: /home/<USER>/.cloudflared/8fe1b450-eb62-4379-b569-dd9c57cba47c.json
protocol: auto
ingress:
  - hostname: jt1-ssh.windcss.com
    service: ssh://localhost:22
  - hostname: jt1-api.windcss.com
    service: http://localhost:8235
  - hostname: jt1-kibana.windcss.com
    service: http://localhost:5601
  - hostname: jt1-kafka.windcss.com
    service: http://localhost:8087
  - hostname: jt1-pg.windcss.com
    service: tcp://localhost:5432
  - hostname: jt1-clickhouse.windcss.com
    service: http://localhost:8123
  - hostname: jt1-minio.windcss.com
    service: http://localhost:7300
  - hostname: jt1-minio-console.windcss.com
    service: http://localhost:7301
  - hostname: jt1-grafana.windcss.com
    service: http://localhost:3080
  - hostname: jt1-grafana-outside.windcss.com
    service: http://localhost:3000
  - hostname: jt1-prometheus.windcss.com
    service: http://localhost:9090
  - hostname: jt1-web.windcss.com
    service: http://localhost:16002
  - service: http_status:404

# cloudflared --loglevel debug --transport-loglevel warn --config /etc/cloudflared/config.yml tunnel run 8fe1b450-eb62-4379-b569-dd9c57cba47c