# --insecure for ssl error
# curl --http2-prior-knowledge -X POST 'http://127.0.0.1:9131/bash?bash=.%2Fmc%20rm%20--force%20--versions%20platform%2Fimages%2F231010%2F32040000400220231010174506443834-70.jpeg'
# ap hub_http2 -e="host=media uri=terminate"
# ap hub_http2 -e="host=media uri=terminate_ch"
# ap hub_http2 -e="host=media port=9135 uri=terminate"
# ap hub_http2 -e="host=liyang_hub uri=terminate"
# ap hub_http2 -e="host=liyang_hub port=9135 uri=terminate"

# ap hub_http2 -e="host=p20 iport=9133 uri=forward?startTime=2023-12-05T07%3A30%3A00.000Z&endTime=2023-12-05T09%3A00%3A00.000Z&withOutMedia=true"
# ap hub_http2 -e="host=points_wujin iport=9133 uri=forward?startTime=2024-06-16T10%3A54%3A00.000Z&endTime=2024-06-16T11%3A36%3A00.000Z&withOutMedia=false"
# ap hub_http2 -e="host=points_xinbei iport=9133 uri=forward?startTime=2024-06-16T10%3A54%3A00.000Z&endTime=2024-06-16T11%3A36%3A00.000Z&withOutMedia=true"
# ap hub_http2 -e="host=hub_area uri=forward?startTime=2024-06-16T10%3A50%3A00.000Z&endTime=2024-06-16T11%3A40%3A00.000Z&withOutMedia=true"
# ap hub_http2 -e="host=xb,wj uri=forward?startTime=2024-06-16T10%3A50%3A00.000Z&endTime=2024-06-16T11%3A40%3A00.000Z&withOutMedia=true"

# ap hub_http2 -e="host=p21,p23 iport=9133 uri=forward?startTime=2024-04-26T12%3A13%3A00.000Z&endTime=2024-04-28T09%3A48%3A00.000Z&withOutMedia=true"
# ap hub_http2 -e="host=l1 uri=hubinfo-path?startTime=2024-02-29T02%3A16%3A00.000Z&endTime=2024-02-29T02%3A50%3A00.000Z"
# ap hub_http2 -e="host=j3,l1,xinbei,wujin timeout=600 uri=sync-vehicles"

# ap hub_http2 -e="host=m1,m2,m3 iport=9135 uri=debug?enableBigdata=false"
# ap hub_http2 -e="host=p1,p2,p3,p4 iport=9132 uri=debug?enableMediaPut=true"
# ap hub_http2 -e="host=m1,m2,m3,j3,l1,xb,wj uri=debug?enableMediaPut=true"

# ap hub_http2 -e="host=j3,l1,xinbei,wujin uri=verifys?startTime=2024-02-29T02%3A16%3A00.000Z&endTime=2024-02-29T07%3A00%3A00.000Z&isUnusual=false"
# ap hub_http2 -e="host=points_czmp iport=9133 uri=verifys?startTime=2024-02-29T02%3A16%3A00.000Z&endTime=2024-02-29T07%3A00%3A00.000Z&isUnusual=false"
# ap hub_http2 -e="host=m2 uri=verifys/32041300400120231010171155684716"

# ap hub_http2 -e="host=points_xinbei iport=9132 uri=download-video?startTime=2024-07-02T01%3A49%3A00.000Z&endTime=2024-07-04T07%3A05%3A00.000Z&overweight=true"
# ap hub_http2 -e="host=p30 iport=9132 uri=debug-offset?offset=0.5&scale=2"
# curl --http2-prior-knowledge -X POST 'http://127.0.0.1:9132/debug-offset?offset=0.7&scale=0.4'

# ap hub_http2 -e="host=m2 timeout=600 uri=statistics"
# ap hub_http2 -e="host=m2 request=GET uri=blacklist-checkraft/1|苏AN6581"
# ap hub_http2 -e="host=m2 uri=blacklist-process"

# ap hub_http2 -e="host=j3 uri=fix110i?startTime=2024-01-29T01%3A38%3A00.000Z&endTime=2024-01-30T09%3A45%3A00.000Z"
# ap hub_http2 -e="host=l1 uri=fix110i? startTime=2024-01-29T01:38:00.000Z endTime=2024-01-30T09:45:00.000Z"

# ap hub_http2 -e="host=m3 uri=recycle-media? startTime=2023-02-01T00:00:00.000Z endTime=2023-03-01T00:00:00.000Z"

- name: http2
  hosts: "{{ host }}"
  gather_facts: no
  serial: 99
  tasks:
    - name: curl
      shell: curl --http2-prior-knowledge -X {{ request | default('POST') }} 'http://127.0.0.1:{{ iport | default(9131) }}/{{ uri }}'
      # shell: curl --http2-prior-knowledge -X {{ request | default('POST') }} {{ data | default('') }} 'http://127.0.0.1:{{ iport | default(9131) }}/{{ uri }}'
      # shell: curl --http2-prior-knowledge -X {{ request | default('POST') }} 'http://127.0.0.1:{{ iport | default(9131) }}/{{ uri }}startTime={{ startTime | urlencode }}&endTime={{ endTime | urlencode }}'
      async: "{{ timeout | default(60) | int }}" # sec
      register: output
    - name: Show results
      debug: msg="{{ output.cmd }} {{ output.stdout }} {{ output.stderr }}"
      # debug: msg="{{ output }}"

# fail: curl urlencode too much characters
# ap hub_http2 -e="host=j3 uri=fix110i data='--data-urlencode \'startTime=\' --data-urlencode \'endTime=2024-01-30T9:45:00.000Z\''"
# ap hub_http2 -e="host=j3 uri=example request=GET data='-G --data-urlencode \'startTime=2024-01-26T01:26:00.000Z\' --data-urlencode \'endTime=2024-01-30T9:45:00.000Z\''"
