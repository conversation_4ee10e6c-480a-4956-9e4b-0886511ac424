- name: sql
  hosts: "{{ inventory }}"
  gather_facts: no
  tasks:
    - postgresql_query:
        login_host: 127.0.0.1
        db: "{{ db }}"
        login_password: "{{ login_password }}"
        port: "{{ pg_port | default(5432) | int }}"
        autocommit: yes # https://docs.ansible.com/ansible/2.9/modules/postgresql_query_module.html#parameters
        # query: DELETE FROM alerting WHERE create_time > '2023-06-12 00:00:00.000000' AND description LIKE '%DOUBLE100%';
        # query: UPDATE databasechangelog set md5sum = '8:29eb86cd70ea73e9e943b6b3ec64ab15' WHERE md5sum = '8:36db4503d1aede7a4cb42d89f93a5e06';
        # query: ALTER TABLE bs_sys_road ALTER COLUMN limit_speed TYPE CHARACTER VARYING(255);
        # query: UPDATE role set suspension_auths = 4;
        # query: SELECT dict_value FROM bs_sys_dict WHERE dict_code = 'DataRetentionDays';
        # query: ALTER TABLE punish_information DROP CONSTRAINT ux_punish_information__check_no;
        # query: >
        #   UPDATE bs_dd_check_weight set is_copy_report = false, is_letter = false, is_paper_notice_letter = false WHERE check_no = '{{item.check_no}}';
        #   DELETE FROM notice_letter WHERE check_no = '{{item.check_no}}';
        # query: DELETE FROM operation_log WHERE operation_category = 'OPERATION';
        # query: UPDATE operation_log set old_data = null, update_data = null WHERE operation_category <> 'Settings';
        # query: DELETE FROM operation_log WHERE operation_category = 'MEDIACLEAN' # AND operation_time < '2023-08-26 00:00:00';
        # query: >
        #   ALTER TABLE bs_dd_check_weight ALTER COLUMN vehicle_no SET NOT NULL;
        #   ALTER TABLE check_weight_incorrect ALTER COLUMN vehicle_no SET NOT NULL;
        #   ALTER TABLE large_transport ALTER COLUMN vehicle_no SET NOT NULL;
        # query: >
        #   CREATE SEQUENCE public.danger_goods_vehicle_log_sequence_generator INCREMENT 1 START WITH 1 CACHE 1;
        #   ALTER TABLE danger_goods_vehicle_log ALTER COLUMN id SET DEFAULT nextval('danger_goods_vehicle_log_sequence_generator'::regclass);
        # query: >
        #   DELETE FROM bs_dd_check_weight WHERE ascii(vehicle_no)<128;
        # query: >
        #   DELETE FROM bs_dd_vehicle_info WHERE vehicle_no !~ '[\u2e80-\ua4cf]|[\uf900-\ufaff]|[\ufe30-\ufe4f]';

        # query: DROP SUBSCRIPTION IF EXISTS platform_sub_p1;
        # query: ALTER SUBSCRIPTION vehicle_sub_p5 SET (slot_name=NONE);
        # query: DROP SUBSCRIPTION IF EXISTS platform_sub_jt;
        # query: CREATE SUBSCRIPTION road_sub CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' PUBLICATION roads_pub WITH (slot_name='roads_slot_{{ inventory }}');
        # query: SELECT * FROM pg_subscription;
        # query: >
        #   CREATE SEQUENCE public.ytqy_lane_sequence_generator INCREMENT 1 START WITH 1000 CACHE 1;
        #   ALTER TABLE bs_sys_ytqy_lane ALTER COLUMN id SET DEFAULT nextval('ytqy_lane_sequence_generator'::regclass);
        # query: DELETE FROM bs_sys_ytqy_lane;
        # query: DELETE FROM bs_sys_site WHERE site_id='10100' OR site_id='10101' OR site_id='10103';
        # query: >
        #   ALTER SUBSCRIPTION road_sub DISABLE;
        #   ALTER SUBSCRIPTION road_sub SET (slot_name=NONE);
        #   DROP SUBSCRIPTION IF EXISTS road_sub;
        query: UPDATE bs_sys_site set org_id = 0;
      register: show
      # loop:
      #   - { check_no: "32041300400220230728131812837645" }
      #   - { check_no: "32041300400220230727155715460475" }
      #   - { check_no: "32041300420220230726164523761284" }
    - name: std
      debug: msg="{{ show.statusmessage }} {{ show.query_result }}"

    # - name: postgresql_query2
    #   postgresql_query:
    #     login_host: 127.0.0.1
    #     db: "{{ db }}"
    #     login_password: "{{ login_password }}"
    #     port: "{{ pg_port | default(5432) | int }}"
    #     autocommit: yes # https://docs.ansible.com/ansible/2.9/modules/postgresql_query_module.html#parameters
    #     query: DROP SUBSCRIPTION IF EXISTS vehicle_sub_p5;
    #   register: show2
    # - name: std
    #   debug: msg="{{ show2.statusmessage }} {{ show2.query_result }}"
