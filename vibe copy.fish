# cp vibe.fish ~/.config/fish/vibe.fish
alias fvibe 'cp /Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts/vibe.fish ~/.config/fish/vibe.fish'

alias codeup 'sudo npm install -g @anthropic-ai/claude-code @google/gemini-cli @augmentcode/auggie'
alias cc 'claude'
alias ccd 'claude --dangerously-skip-permissions'
alias ccr 'claude --resume'
alias cc-debug 'ANTHROPIC_LOG=debug claude --mcp-debug'
function cc-agents
    cp /Users/<USER>/Library/CloudStorage/OneDrive-个人/notes/AI/dev-agent/gemini-analyzer.md ~/.claude/agents/gemini-analyzer.md
    cp /Users/<USER>/Library/CloudStorage/OneDrive-个人/notes/AI/dev-agent/gemini-analyzer-cn.md ~/.claude/agents/gemini-analyzer-cn.md
end

alias gm 'export HTTPS_PROXY="http://127.0.0.1:1087" && gemini'
alias ag 'auggie'

function set_claude_code --argument-names config service local
    if test "$local" = "local"
        mkdir -p .claude && echo $config > .claude/settings.local.json
        echo "Claude Code switched to $service (local)"
    else
        mkdir -p ~/.claude && echo $config > ~/.claude/settings.json
        echo "Claude Code switched to $service"
    end
end

function cc-ar --argument-names local
    set config '{
      "env": {
        "ANTHROPIC_BASE_URL": "https://anyrouter.top",
        "ANTHROPIC_AUTH_TOKEN": "sk-AB8UpPLrNHr2V5SxGUlFSaRpO3cLTmycRrL3MfeNam5IBeWh",
        "ANTHROPIC_DEFAULT_OPUS_MODEL": "claude-opus-4-1-20250805",
        "ANTHROPIC_DEFAULT_SONNET_MODEL": "claude-sonnet-4-20250514",
        "ANTHROPIC_DEFAULT_HAIKU_MODEL": "claude-sonnet-4-20250514"
      }
    }'
    set_claude_code $config "AnyRouter" $local
end

function cc-kimi --argument-names local
    # https://api.moonshot.ai/anthropic
    # sk-4iJI3BIyo8Z90LzFHaZMOyDdfV54GHeo0l2q1WuvZnIdwasQ
    set config '{
      "env": {
        "ANTHROPIC_BASE_URL": "https://api.moonshot.cn/anthropic",
        "ANTHROPIC_AUTH_TOKEN": "sk-V0Fu2nwCLODMNoO8idmh7RckhkNnE6Sj5A6voSlzrxir9nMK",
        "ANTHROPIC_MODEL": "kimi-k2-0905-preview"
      }
    }'
    set_claude_code $config "Kimi" $local
end

function cc-cop --argument-names local
    set config '{
      "env": {
        "ANTHROPIC_BASE_URL": "http://localhost:4141",
        "ANTHROPIC_AUTH_TOKEN": "dummy",
        "ANTHROPIC_DEFAULT_OPUS_MODEL": "gpt-5",
        "ANTHROPIC_DEFAULT_SONNET_MODEL": "claude-sonnet-4",
        "ANTHROPIC_DEFAULT_HAIKU_MODEL": "gpt-4.1"
      }
    }'
    set_claude_code $config "Copilot" $local
    npx --registry=https://registry.npmjs.org copilot-api@latest start --claude-code
end

function cc-ds --argument-names local
    set config '{
      "env": {
        "ANTHROPIC_BASE_URL": "https://api.deepseek.com/anthropic",
        "ANTHROPIC_AUTH_TOKEN": "sk-e17ecf764c84412a857c145a0643a6e4",
        "ANTHROPIC_MODEL": "deepseek-chat"
      }
    }'
    set_claude_code $config "DeepSeek" $local
end


function cc-z --argument-names local
    set config '{
      "env": {
        "ANTHROPIC_BASE_URL": "https://open.bigmodel.cn/api/anthropic",
        "ANTHROPIC_AUTH_TOKEN": "c30cdd0c9f6a4229b6f1a844ac0d17a9.BQMm8o45kOr4gkbK"
      }
    }'
    set_claude_code $config "Zhipu" $local
end
