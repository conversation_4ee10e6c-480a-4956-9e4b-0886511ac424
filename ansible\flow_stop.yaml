# ap flow_stop -t city -K

- name: stop city hub
  hosts: media
  gather_facts: no
  tags: [city]
  tasks:
    # droped, use: ap hub_http2 -e="host=media uri=terminate"
    # - name: terminate flow_hub
    #   uri:
    #     url: http://127.0.0.1:9131/terminate
    #     method: POST
    #   ignore_errors: true
    # - name: terminate flow_verify
    #   uri:
    #     url: http://127.0.0.1:9135/terminate
    #     method: POST
    #   ignore_errors: true

    - name: stop city hik-ytqy
      hosts: y1
      become: yes
      tags: [city]
      tasks:
        - name: kill ytqy process
          shell: |
            ps aux | grep 'target/ytqy-1.0.jar' | awk '{print $2}' | xargs sudo kill -9
