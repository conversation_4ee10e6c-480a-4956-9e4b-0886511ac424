CREATE TABLE cityserver.alerting (
  `id` Int64,
  `check_weight_id` Nullable(String),
  `create_time` Nullable(DateTime64(6)),
  `description` Nullable(String),
  `handle_time` Nullable(DateTime64(6)),
  `handler` Nullable(String),
  `handler_name` Nullable(String),
  `receive_time` Nullable(DateTime64(6)),
  `receiver` Nullable(String),
  `receiver_name` Nullable(String),
  `type` Nullable(String),
  `value` Nullable(String),
) ENGINE = MergeTree() PRIMARY KEY (id)
ORDER BY tuple(id, create_time)