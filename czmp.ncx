<?xml version="1.0" encoding="UTF-8"?>
<Connections Ver="1.5">
	<Connection ConnectionName="69" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="************" Port="5432" Database="CzmpCityServer" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="postgres" Password="0E4BBE8912A07F59EACFDEF49C44EE2C" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="false" SSH_Host="" SSH_Port="22" SSH_UserName="" SSH_AuthenMethod="PASSWORD" SSH_Password="" SSH_SavePassword="false" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="false" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="69t" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="CzmpCityServer" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="postgres" Password="0E4BBE8912A07F59EACFDEF49C44EE2C" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="false" SSH_Host="" SSH_Port="22" SSH_UserName="" SSH_AuthenMethod="PASSWORD" SSH_Password="" SSH_SavePassword="false" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="false" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="center1" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="14003" Database="postgres" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="postgres" Password="0E3F3CD08A00A83058245C3425FC8510" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/ly" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="false" SSH_Host="" SSH_Port="22" SSH_UserName="" SSH_AuthenMethod="PASSWORD" SSH_Password="" SSH_SavePassword="false" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="false" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="center2" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="14000" Database="postgres" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="postgres" Password="0E3F3CD08A00A83058245C3425FC8510" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/125" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="false" SSH_Host="" SSH_Port="22" SSH_UserName="" SSH_AuthenMethod="PASSWORD" SSH_Password="" SSH_SavePassword="false" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="false" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="epidemic1" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5802" Database="epidemic" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="epidemic" Password="0E3F3CD08A00A83058245C3425FC8510" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/epidemic2" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="false" SSH_Host="" SSH_Port="22" SSH_UserName="" SSH_AuthenMethod="PASSWORD" SSH_Password="" SSH_SavePassword="false" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="false" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="jt2" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmp-area" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmp-area" Password="B9F02C6AA55CD729E21FF17B95291AD9" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/y3" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="jt2c" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="localhost" Port="54372" Database="czmp-area" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmp-area" Password="B9F02C6AA55CD729E21FF17B95291AD9" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/jt-tailscale" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="false" SSH_Host="" SSH_Port="22" SSH_UserName="" SSH_AuthenMethod="PASSWORD" SSH_Password="" SSH_SavePassword="false" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="false" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="localhost" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="localhost" Port="5432" Database="postgres" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="postgres" Password="E191AF42327478CC5F143EF279EC4D81" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/localhost" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="false" SSH_Host="" SSH_Port="22" SSH_UserName="" SSH_AuthenMethod="PASSWORD" SSH_Password="" SSH_SavePassword="false" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="false" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="ly" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5433" Database="czmp-area" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="postgres" Password="B9F02C6AA55CD729E21FF17B95291AD9" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/ly" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="lyq" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5433" Database="czmp-area" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="postgres" Password="B9F02C6AA55CD729E21FF17B95291AD9" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/ly" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="u8emvv1l.beesnat.com" SSH_Port="30877" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="m3" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmpcity" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmpcity" Password="706BC5A37208ED28B8709EBC2835F632" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/y3" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p1" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="01ugsom6.beesnat.com" SSH_Port="34037" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="048973492EF0E091D3CC31EEA221258B" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p2" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="01ugsom6.beesnat.com" SSH_Port="34037" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="048973492EF0E091D3CC31EEA221258B" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p3" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="01ugsom6.beesnat.com" SSH_Port="34037" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="048973492EF0E091D3CC31EEA221258B" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p4" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="01ugsom6.beesnat.com" SSH_Port="34037" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="048973492EF0E091D3CC31EEA221258B" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p5" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p5c" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="localhost" Port="54325" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="false" SSH_Host="" SSH_Port="22" SSH_UserName="" SSH_AuthenMethod="PASSWORD" SSH_Password="" SSH_SavePassword="false" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="false" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p5q" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="C9BECB9CC8241CB7163E86B2A2DB2BAE" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="flpt2bny.beesnat.com" SSH_Port="34097" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p6" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p7" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p7q" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="C9BECB9CC8241CB7163E86B2A2DB2BAE" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="flpt2bny.beesnat.com" SSH_Port="34097" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p8" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p11" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p12" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="u8emvv1l.beesnat.com" SSH_Port="30877" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p13" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p14" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="p15" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmppoint" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmppoint" Password="4AA8EB12CFA7774339E467F181C694E5" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/69-tunnel" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="singapore" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="**************" Port="5432" Database="postgres" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="postgres" Password="E4ACD9863F73F98002FCA386DA1B1C9A" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/singapore" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="false" SSH_Host="" SSH_Port="22" SSH_UserName="" SSH_AuthenMethod="PASSWORD" SSH_Password="" SSH_SavePassword="false" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="false" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="wujin" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmp-area" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmp-area" Password="B9F02C6AA55CD729E21FF17B95291AD9" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/ly" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="xb" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="14003" Database="czmp-area" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="postgres" Password="0E4BBE8912A07F59EACFDEF49C44EE2C" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/ly" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="false" SSH_Host="" SSH_Port="22" SSH_UserName="" SSH_AuthenMethod="PASSWORD" SSH_Password="" SSH_SavePassword="false" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="false" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="xbq" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmp-area" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmp-area" Password="B9F02C6AA55CD729E21FF17B95291AD9" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/ly" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="y0" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmpcity" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmpcity" Password="706BC5A37208ED28B8709EBC2835F632" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/y3" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="*************" SSH_Port="20950" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="y1" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmpcity_rep" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmpcity_rep" Password="706BC5A37208ED28B8709EBC2835F632" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/y3" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="y3" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmpcity" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmpcity" Password="706BC5A37208ED28B8709EBC2835F632" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/y3" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="y4" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmpcity" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmpcity" Password="706BC5A37208ED28B8709EBC2835F632" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/y3" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="y5" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmpcity" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmpcity" Password="706BC5A37208ED28B8709EBC2835F632" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/y3" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="y5c" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmpcity" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmpcity" Password="706BC5A37208ED28B8709EBC2835F632" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/y3" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="y5-ssh.socode.fun" SSH_Port="1122" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="y6" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmpcity" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="postgres" Password="3AD0EC223B3D9AB074CC3971D262317F" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/y3" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="tahrxyhy.beesnat.com" SSH_Port="33396" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
	<Connection ConnectionName="y6c" ProjectUUID="" ConnType="POSTGRESQL" OraConnType="" ServiceProvider="Default" Host="*************" Port="5432" Database="czmpcity" OraServiceNameType="" TNS="" MSSQLAuthenMode="" MSSQLAuthenWindowsDomain="" DatabaseFileName="" UserName="czmpcity" Password="706BC5A37208ED28B8709EBC2835F632" SavePassword="true" SettingsSavePath="/Users/<USER>/Library/Application Support/PremiumSoft CyberTech/Navicat CC/Common/Settings/0/0/PostgreSQL/y3" SessionLimit="0" Encoding="" Keepalive="false" KeepaliveInterval="240" MySQLCharacterSet="false" Compression="false" AutoConnect="false" OraRole="" OraOSAuthen="false" SQLiteEncrypt="false" SQLiteEncryptPassword="" SQLiteSaveEncryptPassword="false" UseAdvanced="false" SSL="false" SSL_Authen="false" SSL_PGSSLMode="REQUIRE" SSL_ClientKey="" SSL_ClientCert="" SSL_CACert="" SSL_Clpher="" SSL_PGSSLCRL="" SSL_WeakCertValidation="false" SSL_AllowInvalidHostName="false" SSL_PEMClientKeyPassword="" SSH="true" SSH_Host="y6-ssh.socode.fun" SSH_Port="1122" SSH_UserName="czmp" SSH_AuthenMethod="PASSWORD" SSH_Password="8C19173410345E4176A547FF5A4D0926" SSH_SavePassword="true" SSH_PrivateKey="" SSH_Passphrase="" SSH_SavePassphrase="true" SSH_Compress="false" HTTP="false" HTTP_URL="" HTTP_PA="" HTTP_PA_UserName="" HTTP_PA_Password="" HTTP_PA_SavePassword="" HTTP_EQ="" HTTP_CA="" HTTP_CA_ClientKey="" HTTP_CA_ClientCert="" HTTP_CA_CACert="" HTTP_CA_Passphrase="" HTTP_Proxy="" HTTP_Proxy_Host="" HTTP_Proxy_Port="" HTTP_Proxy_UserName="" HTTP_Proxy_Password="" HTTP_Proxy_SavePassword=""/>
</Connections>

