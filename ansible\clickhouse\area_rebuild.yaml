# ap clickhouse/area_rebuild --extra-vars="host=jintan_clickhouse pg_host=*************:5432"
# ap clickhouse/area_rebuild --extra-vars="host=liyang_clickhouse pg_host=************:5432"
# ap clickhouse/area_rebuild --extra-vars="host=l2 pg_host=************:5432"
# ap clickhouse/area_rebuild --extra-vars="host=l4 pg_host=************:5432"
# ap clickhouse/area_rebuild --extra-vars="host=xinbei pg_host=*************:14003"
# ap clickhouse/area_rebuild --extra-vars="host=wujin pg_host=*************:5432"
# ap clickhouse/area_rebuild --extra-vars="host=p30 pg_host=127.0.0.1:5432 pg_pwd=CZMP.2019 ch_pwd=CZMP.2021"
# ap clickhouse/area_rebuild --extra-vars="host=p31 pg_host=127.0.0.1:5432 pg_pwd=CZMP.2022 ch_pwd=CZMP.2021"

- name: rebuild clickhouse database
  # become: yes
  hosts: "{{ host }}"
  tasks:
    - name: DROP database
      shell: |
        clickhouse-client --password {{ ch_pwd | default("CZMP.2022") }} --port 9030 -nq "DROP DATABASE cityserver_mater_pg_area"
      ignore_errors: true
    - name: CREATE database
      shell: >
        clickhouse-client --password {{ ch_pwd | default("CZMP.2022") }} --port 9030 -nq "
        SET allow_experimental_database_materialized_postgresql=1;
        CREATE DATABASE cityserver_mater_pg_area
        ENGINE = MaterializedPostgreSQL('{{ pg_host }}', 'czmp-area', 'postgres', '{{ pg_pwd | default("CZMP.1019") }}')
        SETTINGS
        materialized_postgresql_tables_list = 'bs_dd_check_weight,bs_dd_vehicle_info,alerting',
        materialized_postgresql_max_block_size=1;
        "
    - name: SHOW TABLES
      shell: |
        clickhouse-client --password {{ ch_pwd | default("CZMP.2022") }} --port 9030 -nq "SHOW TABLES FROM cityserver_mater_pg_area"
      register: show
    - name: std
      debug: msg="{{ show.stderr }} {{ show.stdout }}"
