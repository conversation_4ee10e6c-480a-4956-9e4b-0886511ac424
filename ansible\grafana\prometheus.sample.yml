global:
  scrape_interval: 1m

scrape_configs:
  - job_name: "prometheus"
    scrape_interval: 1m
    static_configs:
    - targets: ["localhost:9090"]

  - job_name: "node"
    static_configs:
    - targets: ["*************:9091", "*************:9091"]

  - job_name: minio
    # bearer_token: <secret> # https://docs.min.io/docs/how-to-monitor-minio-using-prometheus.html
    metrics_path: /minio/v2/metrics/cluster
    scheme: http
    static_configs:
    - targets: ['*************:5804', '*************:5834']

remote_write:
  - url: "https://prometheus-us-central1.grafana.net/api/prom/push"
    basic_auth:
      username: "46556"
      password: "eyJrIjoiODIzYzk2NzNhNjM1YWI2NmU0ZDAxMjhhMTRiZjgzYThhZjVkNmFlOSIsIm4iOiJjem1wLTEyNSIsImlkIjo0NjYzMDR9"