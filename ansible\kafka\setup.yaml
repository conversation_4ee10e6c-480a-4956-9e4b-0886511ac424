# https://docs.ansible.com/ansible/latest/collections/community/docker/docker_compose_module.html

# ap kafka/setup -K --extra-vars="host=p4 data_root=/home/<USER>"
# ap kafka/setup -K --extra-vars="host=p11 user=xbzc"
# ap kafka/setup -K --extra-vars="host=l4n data_root=/mnt/lva env=.env.ly.0 kjaas=kafka_jaas.ly.conf"
# ap kafka/setup -K --extra-vars="host=p32"

- name: Kafka Setup
  hosts: "{{ host }}"
  become: yes
  become_user: "{{ user | default('czmp') }}" # default is 'root'
  gather_facts: yes
  tasks:
    - name: debug through ansible.env
      debug: var=ansible_env.HOME # ansible_env need gather_facts
    - name: standby directory
      file:
        path: ~/kafka # equal
        # or: path: "{{ ansible_env.HOME }}/kafka"
        state: directory
        mode: '0755'
    - name: standby data directory
      file:
        path: "{{ data_root | default('/mnt/data') }}/{{ item.dir }}"
        state: directory
        mode: '0777'
      loop:
        - { dir: kafka_data }
        - { dir: kafka_secrets }
        - { dir: zoo_data }
        - { dir: zoo_secrets }
    - name: compose down
      community.docker.docker_compose_v2:
        state: absent
        project_src: ~/kafka
      ignore_errors: true
    - name: copy .env
      copy:
        src: "{{ env | default('.env') }}"
        dest: ~/kafka/.env
        mode: '0755'
        # mode: '777' # for root
    - name: copy docker-compose.yml
      copy:
        src: ./confluent-sasl.compose.yml
        dest: ~/kafka/docker-compose.yml
        mode: '0755'
    - name: copy kafka_jaas.conf
      copy:
        src: "{{ kjaas | default('./kafka_jaas.conf') }}"
        dest: ~/kafka/kafka_jaas.conf
        mode: '0755'
    - name: copy zookeeper_jaas.conf
      copy:
        src: ./zookeeper_jaas.conf
        dest: ~/kafka/zookeeper_jaas.conf
        mode: '0755'
    - name: compose up
      community.docker.docker_compose_v2:
        state: present
        project_src: ~/kafka
      register: output
    - name: Show results
      ansible.builtin.debug:
        var: output
