CREATE TABLE cityserver.bs_dd_vehicle_info
(
    `check_weight_id` Nullable(String),
    `car_type` Nullable(Int32),
    `dist_code` Nullable(String),
    `company_id` Nullable(String),
    `vehicle_no` String,
    `trailer_no` Nullable(String),
    `plate_color` Nullable(Int32),
    `vehicle_qua_card` Nullable(String),
    `axles` Nullable(Int32),
    `create_time` Nullable(DateTime64(6)),
    `update_time` Nullable(DateTime64(6)),
    `axles_title` Nullable(String),
    `limit_weight` Nullable(Int32),
    `auditter` Nullable(String),
    `company_name` Nullable(String),
    `company_qua` Nullable(String),
    `company_reg` Nullable(String),
    `company_unified_credit` Nullable(String),
    `departure_code` Nullable(String),
    `description` Nullable(String),
    `destination_code` Nullable(String),
    `driver` Nullable(String),
    `driver_id` Nullable(String),
    `driver_qua` Nullable(String),
    `driver_reg` Nullable(String),
    `goods` Nullable(String),
    `hang_vehicle_no` Nullable(String),
    `hang_vehicle_qua` Nullable(String),
    `special` Nullable(UInt8),
    `special_vehicle_type` Nullable(Int32),
    `submitter` Nullable(String),
    `vehicle_qua` Nullable(String),
    `vehicle_reg` Nullable(String),
    `vehicle_axles_type` Nullable(String),
    `in_blacklist` Nullable(UInt8),
    `is_manual_blacklist` Nullable(UInt8),
    `audited` Nullable(UInt8),
    `black_list_operator` Nullable(String),
    `special_operator` Nullable(String),
    `black_list_insert_time` Nullable(DateTime64(6)),
    `black_list_remove_time` Nullable(DateTime64(6)),
    `in_fence` Nullable(UInt8),
    `alerted_fence` Nullable(UInt8),
    `in_blacklist_of_bully` Nullable(UInt8),
    `black_bully_description` Nullable(String),
    `black_bully_operator` Nullable(String),
    `black_bully_insert_time` Nullable(DateTime64(6)),
    `verification_special_platforms` Nullable(String),
    `danger_goods` Nullable(UInt8),
    `danger_goods_operator` Nullable(String)
)
ENGINE = MergeTree() PRIMARY KEY (vehicle_no)
