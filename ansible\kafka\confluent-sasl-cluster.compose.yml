services:
  kafka:
    image: confluentinc/cp-kafka:${CONFLUENT_VERSION:-7.6.1}
    container_name: kafka
    restart: always
    hostname: kafka
    extra_hosts:
      - "host.docker.internal:host-gateway"
    ports:
      - "${EXTERNAL_PORT:-9092}:9092"
      - "29092:29092"
      - "9997:9997"
    environment:
      KAFKA_HEAP_OPTS: $HEAP_OPTS
      KAFKA_BROKER_ID: $BROKER_ID
      KAFKA_ZOOKEEPER_CONNECT: ${ZOOKEEPER_HOST:-zookeeper}:2181
      KAFKA_MESSAGE_MAX_BYTES: $MESSAGE_MAX_BYTES
      KAFKA_DEFAULT_REPLICATION_FACTOR: $REPLICATION_FACTOR
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: $REPLICATION_FACTOR
      KAFKA_NUM_PARTITIONS: $NUM_PARTITIONS
      KAFKA_OFFSETS_TOPIC_NUM_PARTITIONS: $NUM_PARTITIONS

      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: INSIDE:SASL_PLAINTEXT,EXTERNAL:SASL_PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: INSIDE # broker之间进行通信时采用的listener名称
      KAFKA_LISTENERS: INSIDE://:29092,EXTERNAL://:9092 # 默认0.0.0.0:29092,0.0.0.0:9092 for wurstmeister/kafka

      KAFKA_ADVERTISED_LISTENERS: INSIDE://${INSIDE_IP:-host.docker.internal}:29092,EXTERNAL://${EXTERNAL_IP:-host.docker.internal}:${EXTERNAL_PORT:-9092}
      KAFKA_ADVERTISED_HOST_NAME: ${INSIDE_IP:-host.docker.internal} # for cluster identify

      KAFKA_JMX_PORT: 49999 # for confluentinc/cp-kafka
      JMX_PORT: 49997 
      KAFKA_JMX_HOSTNAME: ${INSIDE_IP:-host.docker.internal}

      KAFKA_OPTS: >-
        -Djava.security.auth.login.config=/etc/kafka/kafka_jaas.conf
        -Dzookeeper.sasl.client=true
        -Dzookeeper.sasl.clientconfig=Client

      KAFKA_SASL_ENABLED_MECHANISMS: PLAIN,SCRAM-SHA-256
      KAFKA_SASL_MECHANISM_INTER_BROKER_PROTOCOL: PLAIN # SCRAM-SHA-256
      KAFKA_SUPER_USERS: "User:admin;User:cluster"
      KAFKA_AUTHORIZER_CLASS_NAME: "kafka.security.authorizer.AclAuthorizer" # https://stackoverflow.com/a/70582805/346701
      KAFKA_ZOOKEEPER_SASL_ENABLED: "true"
      KAFKA_ZOOKEEPER_SET_ACL: "true"
      KAFKA_ALLOW_EVERYONE_IF_NO_ACL_FOUND: "false"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - type: bind
        source: ./kafka_jaas.conf
        target: /etc/kafka/kafka_jaas.conf
        read_only: true
      - type: bind
        source: $KAFKA_DATA
        target: /var/lib/kafka/data
      - type: bind
        source: $KAFKA_SECRETS
        target: /etc/kafka/secrets
