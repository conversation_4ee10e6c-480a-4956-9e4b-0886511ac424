# cp vibe.ps1 C:\Users\<USER>\Documents\PowerShell\vibe.ps1

# Aliases and Functions
function codeup { npm install -g @anthropic-ai/claude-code @google/gemini-cli @augmentcode/auggie }
Set-Alias -Name cc -Value claude
function ccd { claude --dangerously-skip-permissions }
function ccr { claude --resume }
function cc-debug { $env:ANTHROPIC_LOG = "debug"; claude --mcp-debug }
function gm { $env:HTTPS_PROXY = "http://127.0.0.1:1087"; gemini }
Set-Alias -Name ag -Value auggie

function Set-ClaudeCode {
  param(
    [Parameter(Mandatory = $true)]
    [string]$Name,
        
    [Parameter(Mandatory = $true)]
    [string]$Envs,
        
    [Parameter(Mandatory = $false)]
    [string]$Local
  )
    
  if ($Local -eq "local") {
    $file = ".claude/settings.local.json"
    New-Item -ItemType Directory -Path ".claude" -Force | Out-Null
  }
  else {
    $file = "$env:USERPROFILE/.claude/settings.json"
    New-Item -ItemType Directory -Path "$env:USERPROFILE/.claude" -Force | Out-Null
  }
    
  if (Test-Path $file) {
    $existing = Get-Content $file -Raw
  }
  else {
    $existing = '{}'
  }
    
  # Use jq to merge the JSON (requires jq to be installed)
  $existing | jq --argjson env $Envs '. + {"env": $env}' | Out-File -FilePath $file -Encoding UTF8
  Write-Host "Claude Code switched to $Name $Local"
}

function cc-any {
  param(
    [Parameter(Mandatory = $false)]
    [string]$Local
  )
    
  $env_config = @'
{
    "ANTHROPIC_BASE_URL": "https://anyrouter.top",
    "ANTHROPIC_AUTH_TOKEN": "sk-AB8UpPLrNHr2V5SxGUlFSaRpO3cLTmycRrL3MfeNam5IBeWh",
    "ANTHROPIC_DEFAULT_OPUS_MODEL": "claude-opus-4-1-20250805",
    "ANTHROPIC_DEFAULT_SONNET_MODEL": "claude-sonnet-4-20250514",
    "ANTHROPIC_DEFAULT_HAIKU_MODEL": "claude-sonnet-4-20250514"
}
'@
  Set-ClaudeCode -Name "AnyRouter" -Envs $env_config -Local $Local
}

function cc-kimi {
  param(
    [Parameter(Mandatory = $false)]
    [string]$Local
  )
    
  # https://api.moonshot.ai/anthropic
  # sk-4iJI3BIyo8Z90LzFHaZMOyDdfV54GHeo0l2q1WuvZnIdwasQ
  $env_config = @'
{
    "ANTHROPIC_BASE_URL": "https://api.moonshot.cn/anthropic",
    "ANTHROPIC_AUTH_TOKEN": "sk-V0Fu2nwCLODMNoO8idmh7RckhkNnE6Sj5A6voSlzrxir9nMK",
    "ANTHROPIC_MODEL": "kimi-k2-0905-preview"
}
'@
  Set-ClaudeCode -Name "Kimi" -Envs $env_config -Local $Local
}

function cc-cop {
  param(
    [Parameter(Mandatory = $false)]
    [string]$Local
  )
    
  $env_config = @'
{
    "ANTHROPIC_BASE_URL": "http://localhost:4141",
    "ANTHROPIC_AUTH_TOKEN": "dummy",
    "ANTHROPIC_DEFAULT_OPUS_MODEL": "gpt-5",
    "ANTHROPIC_DEFAULT_SONNET_MODEL": "claude-sonnet-4",
    "ANTHROPIC_DEFAULT_HAIKU_MODEL": "gpt-4.1"
}
'@
  Set-ClaudeCode -Name "Copilot" -Envs $env_config -Local $Local
  npx --registry=https://registry.npmjs.org copilot-api@latest start --claude-code
}

function cc-ds {
  param(
    [Parameter(Mandatory = $false)]
    [string]$Local
  )
    
  $env_config = @'
{
    "ANTHROPIC_BASE_URL": "https://api.deepseek.com/anthropic",
    "ANTHROPIC_AUTH_TOKEN": "sk-e17ecf764c84412a857c145a0643a6e4",
    "ANTHROPIC_MODEL": "deepseek-chat"
}
'@
  Set-ClaudeCode -Name "DeepSeek" -Envs $env_config -Local $Local
}

function cc-z {
  param(
    [Parameter(Mandatory = $false)]
    [string]$Local
  )
    
  $env_config = @'
{
    "ANTHROPIC_BASE_URL": "https://open.bigmodel.cn/api/anthropic",
    "ANTHROPIC_AUTH_TOKEN": "c30cdd0c9f6a4229b6f1a844ac0d17a9.BQMm8o45kOr4gkbK"
}
'@
  Set-ClaudeCode -Name "Zhipu" -Envs $env_config -Local $Local
}

# Functions are automatically available when dot-sourced
