tunnel: 484eb512-824d-4657-ac61-cb18cceda0e9
credentials-file: /home/<USER>/.cloudflared/484eb512-824d-4657-ac61-cb18cceda0e9.json
protocol: auto
ingress:
  - hostname: jt3-ssh.socode.top
    service: ssh://localhost:22
  - hostname: jt3-api.socode.top
    service: http://localhost:8235
  - hostname: jt3-api-flow.socode.top
    service: http://localhost:9132
  - hostname: jt3-kibana.socode.top
    service: http://localhost:5601
  - hostname: jt3-kafka-87.socode.top
    service: http://localhost:8087
  - hostname: jt3-pg.socode.top
    service: tcp://localhost:5432
  - hostname: jt3-clickhouse.socode.top
    service: http://localhost:8123
  - hostname: jt3-minio.socode.top
    service: http://localhost:7300
  - hostname: jt3-minio-console.socode.top
    service: http://localhost:7301
  - hostname: jt3-grafana.socode.top
    service: http://localhost:3080
  - hostname: jt3-web.socode.top
    service: http://localhost:16002
  - hostname: jt3-hub.socode.top
    service: http://localhost:9131
  - service: http_status:404

# cloudflared tunnel route dns jt3 jt3-ssh.socode.top
# cloudflared tunnel route dns jt3 jt3-api.socode.top
# cloudflared tunnel route dns jt3 jt3-api-flow.socode.top
# cloudflared tunnel route dns jt3 jt3-kibana.socode.top
# cloudflared tunnel route dns jt3 jt3-kafka-87.socode.top
# cloudflared tunnel route dns jt3 jt3-pg.socode.top
# cloudflared tunnel route dns jt3 jt3-clickhouse.socode.top
# cloudflared tunnel route dns jt3 jt3-minio.socode.top
# cloudflared tunnel route dns jt3 jt3-minio-console.socode.top
# cloudflared tunnel route dns jt3 jt3-grafana.socode.top
# cloudflared tunnel route dns jt3 jt3-web.socode.top
# cloudflared tunnel route dns jt3 jt3-hub.socode.top

# cloudflared --loglevel debug --transport-loglevel warn --config /etc/cloudflared/config.yml tunnel run 71e0bb7a-da77-43e0-bad3-26ad82f0f46b