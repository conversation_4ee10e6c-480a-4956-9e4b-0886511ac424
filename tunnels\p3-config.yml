tunnel: 33756221-ee5b-43e3-a7cd-30e2338063e1
credentials-file: /home/<USER>/.cloudflared/33756221-ee5b-43e3-a7cd-30e2338063e1.json
protocol: auto
ingress:
  - hostname: p3-ssh.socode.top
    service: ssh://localhost:22
  - hostname: p3-api.socode.top
    service: http://localhost:80
  - hostname: p3-hub.socode.top
    service: http://localhost:9133
  - hostname: p3-kibana.socode.top
    service: http://localhost:5601
  - hostname: p3-kafka-87.socode.top
    service: http://localhost:8087
  - hostname: p3-pg.socode.top
    service: tcp://localhost:5432
  - hostname: p3-clickhouse.socode.top
    service: http://localhost:8123
  - hostname: p3-minio.socode.top
    service: http://localhost:9000
  - hostname: p3-minio-console.socode.top
    service: http://localhost:9001
  - hostname: p3-grafana.socode.top
    service: http://localhost:3080
  - hostname: p3-web.socode.top
    service: http://localhost:16002
  - service: http_status:404

# cloudflared tunnel route dns p3 p3-ssh.socode.top
# cloudflared tunnel route dns p3 p3-api.socode.top
# cloudflared tunnel route dns p3 p3-hub.socode.top
# cloudflared tunnel route dns p3 p3-web.socode.top
# cloudflared tunnel route dns p3 p3-kibana.socode.top
# cloudflared tunnel route dns p3 p3-kafka-87.socode.top
# cloudflared tunnel route dns p3 p3-pg.socode.top
# cloudflared tunnel route dns p3 p3-clickhouse.socode.top
# cloudflared tunnel route dns p3 p3-minio.socode.top
# cloudflared tunnel route dns p3 p3-minio-console.socode.top
# cloudflared tunnel route dns p3 p3-grafana.socode.top