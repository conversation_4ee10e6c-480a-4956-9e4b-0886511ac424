# https://raw.githubusercontent.com/grafana/loki/v3.5.3/production/docker-compose.yaml

networks:
  loki:

services:
  loki:
    container_name: loki
    image: grafana/loki:3.5.3
    ports:
      - "3100:3100"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./loki-config.yaml:/etc/loki/local-config.yaml
    networks:
      - loki
    
  promtail:
    container_name: promtail
    image: grafana/promtail:3.5.3
    restart: always
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: -config.file=/etc/promtail/config.yml
    volumes:
      - ./promtail-config.yaml:/etc/promtail/config.yml
      - /var/log:/var/log
    ports:
      - "9080:9080"
    networks:
      - loki
