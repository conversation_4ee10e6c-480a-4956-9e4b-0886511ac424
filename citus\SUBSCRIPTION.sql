\c replicate
CREATE SUBSCRIPTION cdc_sub_checks_3
        CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' 
        PUBLICATION cdc_pub_checks
        WITH (copy_data=true,create_slot=false,slot_name='cdc_slot_checks');
CREATE SUBSCRIPTION cdc_sub_checks_4
        CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' 
        PUBLICATION cdc_pub_checks
        WITH (copy_data=true,create_slot=false,slot_name='cdc_slot_checks');
CREATE SUBSCRIPTION cdc_sub_checks_5
        CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' 
        PUBLICATION cdc_pub_checks
        WITH (copy_data=true,create_slot=false,slot_name='cdc_slot_checks');
CREATE SUBSCRIPTION cdc_sub_checks_6
        CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' 
        PUBLICATION cdc_pub_checks
        WITH (copy_data=true,create_slot=false,slot_name='cdc_slot_checks');


CREATE SUBSCRIPTION cdc_sub_vehicle_3
        CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' 
        PUBLICATION cdc_pub_vehicle
        WITH (copy_data=true,create_slot=false,slot_name='cdc_slot_vehicle');
CREATE SUBSCRIPTION cdc_sub_vehicle_4
        CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' 
        PUBLICATION cdc_pub_vehicle
        WITH (copy_data=true,create_slot=false,slot_name='cdc_slot_vehicle');
CREATE SUBSCRIPTION cdc_sub_vehicle_5
        CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' 
        PUBLICATION cdc_pub_vehicle
        WITH (copy_data=true,create_slot=false,slot_name='cdc_slot_vehicle');
CREATE SUBSCRIPTION cdc_sub_vehicle_6
        CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' 
        PUBLICATION cdc_pub_vehicle
        WITH (copy_data=true,create_slot=false,slot_name='cdc_slot_vehicle');


CREATE SUBSCRIPTION cdc_sub_log_3
        CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' 
        PUBLICATION cdc_pub_log
        WITH (copy_data=true,create_slot=false,slot_name='cdc_slot_log');
CREATE SUBSCRIPTION cdc_sub_log_4
        CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' 
        PUBLICATION cdc_pub_log
        WITH (copy_data=true,create_slot=false,slot_name='cdc_slot_log');
CREATE SUBSCRIPTION cdc_sub_log_5
        CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' 
        PUBLICATION cdc_pub_log
        WITH (copy_data=true,create_slot=false,slot_name='cdc_slot_log');
CREATE SUBSCRIPTION cdc_sub_log_6
        CONNECTION 'dbname=czmpcity host=************* port=5432 user=czmpcity password=CZMP^2023' 
        PUBLICATION cdc_pub_log
        WITH (copy_data=true,create_slot=false,slot_name='cdc_slot_log');

\dRs
SELECT * FROM pg_subscription;
SELECT * FROM pg_subscription_rel;
-- i 初始化中 d 拷贝数据中 s 同步已完成 r 正常复制中

ALTER SUBSCRIPTION cdc_sub_checks_3 DISABLE;
ALTER SUBSCRIPTION cdc_sub_checks_3 ENABLE;
ALTER SUBSCRIPTION cdc_sub_checks_3 REFRESH PUBLICATION;

DROP SUBSCRIPTION IF EXISTS cdc_sub_checks_3;
DROP SUBSCRIPTION IF EXISTS cdc_sub_checks_4;
DROP SUBSCRIPTION IF EXISTS cdc_sub_checks_5;
DROP SUBSCRIPTION IF EXISTS cdc_sub_checks_6;
DROP SUBSCRIPTION IF EXISTS cdc_sub_vehicle_3;
DROP SUBSCRIPTION IF EXISTS cdc_sub_vehicle_4;
DROP SUBSCRIPTION IF EXISTS cdc_sub_vehicle_5;
DROP SUBSCRIPTION IF EXISTS cdc_sub_vehicle_6;
DROP SUBSCRIPTION IF EXISTS cdc_sub_log_3;
DROP SUBSCRIPTION IF EXISTS cdc_sub_log_4;
DROP SUBSCRIPTION IF EXISTS cdc_sub_log_5;
DROP SUBSCRIPTION IF EXISTS cdc_sub_log_6;

TRUNCATE bs_dd_check_weight, bs_dd_vehicle_info, operation_log;

DROP DATABASE replicate WITH (FORCE);
-- or: sudo -u postgres dropdb replicate -f

select create_time from bs_dd_check_weight order by create_time desc limit 1;
select count(*) from bs_dd_check_weight;