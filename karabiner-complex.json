{"title": "Complex apps", "rules": [{"description": "Complex apps by left control & option+letters.", "setup_path": "~/.config/karabiner/assets/complex_modifications/karabiner-complex.json", "manipulators": [{"type": "basic", "from": {"key_code": "a", "modifiers": {"mandatory": ["left_control", "left_option"], "optional": ["caps_lock"]}}, "to": [{"shell_command": "open '/System/Applications/Utilities/Activity Monitor.app'"}]}, {"type": "basic", "from": {"key_code": "d", "modifiers": {"mandatory": ["left_control", "left_option"], "optional": ["caps_lock"]}}, "to": [{"shell_command": "open '/System/Applications/Dictionary.app'"}]}, {"type": "basic", "from": {"key_code": "e", "modifiers": {"mandatory": ["left_control", "left_option"], "optional": ["caps_lock"]}}, "to": [{"shell_command": "open '/Applications/Visual Studio Code.app'"}]}, {"type": "basic", "from": {"key_code": "f", "modifiers": {"mandatory": ["left_control", "left_option"], "optional": ["caps_lock"]}}, "to": [{"shell_command": "open ~"}]}, {"type": "basic", "from": {"key_code": "w", "modifiers": {"mandatory": ["left_option"], "optional": ["caps_lock"]}}, "to": [{"shell_command": "osascript /Users/<USER>/translater.scpt"}]}, {"type": "basic", "from": {"key_code": "t", "modifiers": {"mandatory": ["left_control", "left_option"], "optional": ["caps_lock"]}}, "to": [{"shell_command": "open '/Applications/iTerm.app'"}]}]}]}