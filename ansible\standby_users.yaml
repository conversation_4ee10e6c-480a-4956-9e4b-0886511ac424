# ap standby_users -K -e="host=y0,y1,y2,y3,y4,y5,y6"
# ap standby_users -K -e="host=m1,m2,m3"
# cat /etc/passwd

- name: Users
  hosts: "{{ host }}"
  become: yes
  tasks:
    - name: Add sysadmin
      ansible.builtin.user:
        name: sysadmin
        comment: SysAdmin
        password: "{{ 'Cz.2023+' | password_hash('sha512') }}"
        update_password: "always"
    - name: Add shenji
      ansible.builtin.user:
        name: shenji
        comment: ShenJi
        password: "{{ 'Cz.2023+' | password_hash('sha512') }}"
        update_password: "always"
    - name: Add anquan
      ansible.builtin.user:
        name: anquan
        comment: AnQuan
        password: "{{ 'Cz.2023+' | password_hash('sha512') }}"
        update_password: "always"
