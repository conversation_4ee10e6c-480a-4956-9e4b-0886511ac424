# ap spring_health -t points

- name: points health
  hosts: points
  gather_facts: no
  tags: [points]
  tasks:
    - name: management/health
      uri:
        url: http://127.0.0.1:80/management/health
        method: GET
        return_content: true
      ignore_errors: true
      register: show
    - name: std
      debug: msg="{{ show.status }} {{ show.content }}"

- name: area health
  hosts: [j1, l1, wujin, xinbei]
  gather_facts: no
  tags: [area]
  tasks:
    - name: management/health
      uri:
        url: http://127.0.0.1:8235/management/health
        method: GET
        return_content: true
      ignore_errors: true
      register: show
    - name: std
      debug: msg="{{ show.status }} {{ show.content }}"

- name: city health
  hosts: y1
  gather_facts: no
  tags: [city]
  tasks:
    - name: management/health
      uri:
        url: http://127.0.0.1:8080/management/health
        method: GET
        return_content: true
      ignore_errors: true
      register: show
    - name: std
      debug: msg="{{ show.status }} {{ show.content }}"