apiVersion: v1
kind: Service
metadata:
  name: cityserver-service
spec:
  selector:
    app: spring
  type: NodePort
  ports:
    - protocol: TCP
      targetPort: 8080
      port: 8080
      nodePort: 30000

# kubectl expose deployment cityserver-deployment --port=6666 --target-port=8080
# kubectl get service
# curl -X POST http://10.233.32.107:8080/api/authenticate
# curl -X POST http://10.233.32.107:8080/api/authenticate --header 'Content-Type: application/json' --data-raw '{"username": "system","password": "Czmp@20+23=2023"}'

# kubectl -n default rollout restart service/cityserver-service
