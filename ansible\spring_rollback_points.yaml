# ap spring_issus -K --extra-vars="host=points_jintan"

- name: spring rollback
  hosts: "{{ host }}"
  become: yes
  gather_facts: no
  tasks:
    - name: stop container
      community.docker.docker_container:
        name: cityserver_point
        state: absent
      ignore_errors: true
    # - name: login
    #   community.docker.docker_login:
    #     registry_url: registry.cn-shanghai.aliyuncs.com
    #     username: <EMAIL>
    #     password: z565656z
    - name: compose up
      community.docker.docker_compose:
        project_name: cityserver_point
        definition:
          version: "3.8"
          services:
            cityserver_point:
              image: sha256:aa7c06df9d7a4595a0e0ffb26e0a5f015590911a735955aa3c3b9d27cb5f42ab
              container_name: cityserver_point
              restart: on-failure
              extra_hosts:
                - 'host.docker.internal:host-gateway'
              environment:
                - _JAVA_OPTIONS=-Xmx4g -Xms1g --add-exports=java.base/sun.security.action=ALL-UNNAMED
                - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-point}
                - JHIPSTER_LOGGING_LOGSTASH_HOST=host.docker.internal
                - POINT_ID=3204130042
              volumes:
                - /usr/share/fonts:/usr/share/fonts
              ports:
                - '80:8080'
      register: output

    - name: Show results
      ansible.builtin.debug:
        var: output