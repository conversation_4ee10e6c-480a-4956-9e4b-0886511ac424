# ap hksdk -K --extra-vars="host=p12,p13,p15"
# ap hksdk -K --extra-vars="host=p1_root,p2_root,p3_root,p4_root" # for create /var/lib/hksdk
# ap hksdk -K --extra-vars="host=p4c"

# - name: download sdk
#   hosts: p11
#   gather_facts: no
#   tasks:
#     - ansible.posix.synchronize:
#         mode: pull
#         src: /var/lib/hksdk
#         dest: ~/work/
#         archive: true
#         recursive: true
#         delete: true

# - name: standby directory
#   hosts: "{{ host }}"
#   tasks:
#     - file:
#         path: /var/lib/hksdk
#         state: directory
#         mode: "0755"
#         owner: "{{ user | default('czmp') }}"
#         group: "{{ user | default('czmp') }}"
#       ignore_errors: true

- name: upload sdk
  hosts: "{{ host }}"
  become: yes
  tasks:
    - ansible.posix.synchronize:
        src: ~/work/czmp/hksdk
        dest: /var/lib/
        archive: true
        recursive: true
        delete: true