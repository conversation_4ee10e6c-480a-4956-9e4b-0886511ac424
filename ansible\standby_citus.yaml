# ansible-playbook -i inventory.yaml standby_citus.yaml
# https://docs.ansible.com/ansible/2.9/modules/postgresql_pg_hba_module.html
# https://docs.citusdata.com/en/v11.3/admin_guide/cluster_management.html#increasing-worker-security

- name: standby citus
  hosts: city_citus
  gather_facts: no
  tasks:
    # - name: pg_hba.conf
    #   postgresql_pg_hba:
    #     dest: /etc/postgresql/15/main/pg_hba.conf
    #     contype: host
    #     users: all
    #     databases: all
    #     source: "{{ item.source }}"
    #     method: trust
    #   loop:
    #     - { source: '*************/32' }
    #     - { source: '*************/32' }
    #     - { source: '*************/32' }
    #     - { source: '*************/32' }
    # - name: pg_hba.conf trust 127.0.0.1
    #   postgresql_pg_hba:
    #     dest: /etc/postgresql/15/main/pg_hba.conf
    #     contype: host
    #     users: all
    #     databases: all
    #     source: 127.0.0.1/32
    #     method: trust
    # - name: pg_hba.conf trust ::1/128
    #   postgresql_pg_hba:
    #     dest: /etc/postgresql/15/main/pg_hba.conf
    #     contype: host
    #     users: all
    #     databases: all
    #     source: ::1/128
    #     method: trust
    # - name: pg_hba.conf password
    #   postgresql_pg_hba:
    #     dest: /etc/postgresql/15/main/pg_hba.conf
    #     contype: host
    #     users: all
    #     source: 0.0.0.0/0
    #     databases: all
    #     method: password
    # - name: sync pgpass
    #   ansible.posix.synchronize:
    #     src: .pgpass
    #     dest: /var/lib/postgresql
    - name: chomd
      file:
        path: /var/lib/postgresql/.pgpass
        mode: '0600'
    # - name: pg_reload_conf()
    #   postgresql_query:
    #     login_host: 127.0.0.1
    #     db: czmpcity
    #     login_password: CZMP^2023
    #     port: 5432
    #     query: SELECT pg_reload_conf();
