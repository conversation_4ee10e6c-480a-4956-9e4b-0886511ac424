# $endpoint = "10.54.113.237"
$endpoint = "p1-api.socode.top"

$account = @"
{
    `"username`": `"admin`",
    `"password`": `"Cz.3202&`"
}
"@

$p2 = "0.98"
$p3 = "0.98"
$p4 = "0.98"
$p5 = "0.98"
$p6 = "0.98"

$headers = New-Object "System.Collections.Generic.Dictionary[[String],[String]]"
$headers.Add("Content-Type", "application/json")

$response_auth = Invoke-RestMethod "http://$endpoint/api/authenticate?version=Cz.3202%26" -Method 'POST' -Headers $headers -Body $account
# $response_auth | ConvertTo-Json
$id_token = $response_auth.id_token

$headers.Add("Authorization", "Bearer $id_token")

function UpdatePercent {
  param (
    $axles,
    $scale
  )
  Write-Output "UpdateScaleWeightPercent axles:$axles scale:$scale"

  $response = Invoke-RestMethod "http://$endpoint/api/dict-czmp?dictCode=ScaleWeightPercent_$axles" -Headers $headers
  # $response | ConvertTo-Json
  $id = $response.id

  $body = "{`"id`": $id, `"dictValue`": `"$scale`"}"
  # Write-Output $body

  $response_put = Invoke-RestMethod "http://$endpoint/api/bs-sys-dicts/$id" -Method 'PATCH' -Headers $headers -Body $body
  $response_put | ConvertTo-Json
}

UpdatePercent -axles 2 -scale $p2;
UpdatePercent -axles 3 -scale $p3;
UpdatePercent -axles 4 -scale $p4;
UpdatePercent -axles 5 -scale $p5;
UpdatePercent -axles 6 -scale $p6;
