做一个“养老院入住管理系统”网站。
基于以下所有信息构筑思考产品逻辑、交互流程。然后设计所有需要的页面。
先规划设计任务，任务尽量专注、内聚。按步骤迭代实施。

## 功能模块
1. 营销管理模块 - 记录咨询接待详细，包括咨询人信息、长者情况、咨询目的及详情，写跟进记录功能，支持统计销售员的咨询接待次数、跟进次数，签约量，统计媒介渠道的咨询接待次数、签约量。
2. 居住管理模块 - 为了更好地进行院内居住管理，整个流程应包括入住管理和退住管理两个板块，入住管理需支持入住总览，预订管理，入住申请，入住评估，入住审核，签订合同，入住办理，入住缴费，房间确认等功能，退住管理需包括退住总览，退住申请，退住审核，退住缴费等功能。
3. 在住服务模块 - 老人入住后的服务模块，需包括住下后的在住总览，请假申请，请假审核，销假管理，床位调整，床位对调，事故报告，探访登记。护理服务方面应包括：长者生活记录类型，护理项目，护理计划，护理记录，护理变更，护理核查，生活记录，照护看板查询（时间维度），照护看板查询（长者维度），护理排班，班次设置，岗位管理，交接班管理，交接班模板，护理项目，护理成员，护理小组，护理知识库，合同跟踪，合同模版，合同模板录入项等功能。
4. 费用结算板块 - 为了实现整个机构入驻过程中老人费用的精细化管理，费用管理板块应包括：费用账单管理，费用调整，费用结算，预缴月费用，缴费通知，缴费明细，费用查询，月费用报表，未预缴月费用查询，欠费查询，个人账户，账户记录，日常消费，消费记录，抄表管理，系统费项，月费用标准，入住初始费用，阶段性费用设置，护理等级及费用，仪表收费标准，房间仪表管理，合并分组设置，会员折扣，预缴折扣，请假规则等功能
5. 库存管理板块 - 养老机构避免不了会有各种物料流转，为了对机构物品进行精细化管理，需具备库存管理模块，具体功能需包括库存查询，入库管理，出库管理，物品调拨，调拨审核，库存调整，盘点记录，订单流水，仓库管理，供应商管理功能。

## User Interface
界面设计风格倾向于现代、精致、专业。主色调蓝色，传达信任感。
页面语言以中文为主。
兼容 Mobile 端
这里给出首页的设计要求，作为参考。其它页面自行规划设计。
1. 左侧边栏：提供系统的主要功能导航，包括入住总览、预订管理、入住申请等核心功能模块。可以考虑多级导航。
2. 顶部导航：包含搜索框、通知中心和用户信息区。
3. 主内容区：采用卡片式布局，包含数据概览、趋势图表、申请列表和入住流程图。

## 技术栈
- nodejs/typescript/pnpm/Next.js(App Router)
- PostgreSQL/supabase/drizzle/better-auth
  - or neon/Cloudflare R2 replace supabase
- tailwindcss/shadcn/ECharts/framer-motion
- zod/zustand/react-hook-form

## 数据结构
仅为参考，如果你认为相对功能模块设计有缺失或错误，可以自行修改。

### 1. 用户管理模块

#### 1.1 用户账号表 (app_user)
- 主键 (bigint)
- 角色标识 (int)
- 昵称 (字符串)
- 用户名或手机号 (字符串)
- 密码 (字符串)
- 手机号 (字符串)
- 真实姓名 (字符串)
- 性别 (整数)
- 头像 (字符串)
- 个人简介 (文本)
- 账号状态 (整数)
- 创建时间 (日期时间)
- 更新时间 (日期时间)
- 最后登录IP (字符串)
- 最后登录时间 (日期时间)
- 注销时间 (日期时间)
- 删除标识 (整数)
- 系统标识 (整数)
- 外部系统导入标识 (整数)

#### 1.2 系统管理员用户表 (sys_mgr_user)
- 用户标识 (bigint)
- 主键 (bigint)
- 部门标识 (bigint)
- 用户账号 (字符串)
- 用户昵称 (字符串)
- 用户类型 (字符串)
- 用户邮箱 (字符串)
- 手机号码 (字符串)
- 用户性别 (字符)
- 头像地址 (字符串)
- 密码 (字符串)
- 省 (字符串)
- 市 (字符串)
- 区 (字符串)
- 帐号状态 (字符)
- 删除标志 (字符)
- 最后登录IP (字符串)
- 最后登录时间 (日期时间)
- 创建时间 (日期时间)
- 更新时间 (日期时间)
- 备注 (字符串)

### 2. 老人信息管理模块

#### 2.1 老人基本信息表 (elder_info)
- 主键 (bigint)
- 老人标识 (bigint) - 外键关联APP用户表(app_user.id)
- 房间标识 (int) - 外键关联房间表(elder_room.id)
- 照片 (字符串)
- 真实姓名 (字符串)
- 年龄 (整数)
- 手机号 (字符串)
- 性别 (整数)
- 身份证 (字符串)
- 生日 (日期)
- 省 (字符串)
- 市 (字符串)
- 区 (字符串)
- 社区 (字符串)
- 户籍地址 (字符串)
- 现居详细地址 (字符串)
- 民族 (字符串)
- 文化程度 (字符串)
- 婚姻状况 (字符串)
- 紧急联系人姓名 (字符串)
- 紧急联系人性别 (整数)
- 紧急联系人电话 (字符串)
- 关系 (字符串)
- 工作单位 (字符串)
- 创建时间 (日期时间)
- 修改时间 (日期时间)
- 注销时间 (日期时间)
- 删除标识 (整数)

#### 2.2 老人健康详情表 (elder_health_info)
- 主键 (bigint)
- 老人标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 血型 (整数)
- 健康状况 (字符串)
- 健康标签 (字符串)
- 生活能力 (字符串)
- 自理能力 (字符串)
- 护理等级 (字符串)
- 睡眠质量 (字符串)
- 吸烟频率 (字符串)
- 饮酒频率 (字符串)
- 运动频率 (字符串)
- 饮食偏好 (字符串)
- 创建时间 (日期时间)
- 修改时间 (日期时间)
- 删除标识 (整数)

#### 2.3 老人健康监测数据表 (elder_health_daily_data)
- 主键 (bigint)
- 老人标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 体温 (浮点数)
- 浅度睡眠时间 (整数)
- 深度睡眠时间 (整数)
- 今天行走步数 (整数)
- 心率 (整数)
- 舒张压 (浮点数)
- 收缩压 (浮点数)
- 血糖 (浮点数)
- 血氧 (浮点数)
- 定位地址 (字符串)
- 创建时间 (日期时间)

#### 2.4 老人评估记录表 (elder_assessment)
- 主键 (bigint)
- 长者标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 评估日期 (日期)
- 评估人 (字符串)
- 生活能力等级 (整数)
- 护理等级 (整数)
- 认知状况 (文本)
- 身体状况 (文本)
- 心理状况 (文本)
- 评估结果 (文本)
- 备注 (文本)
- 创建时间 (日期时间)
- 更新时间 (日期时间)
- 创建人 (字符串)
- 更新人 (字符串)

### 3. 房间管理模块

#### 3.1 房间信息表 (elder_room)
- 主键 (bigint)
- 老人标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 房间编号 (字符串)
- 楼栋 (字符串)
- 楼层 (整数)
- 房间类型 (字符串)
- 床位数量 (整数)
- 床位号 (整数)
- 设施设备 (文本)
- 状态 (整数)
- 房间费用 (浮点数)
- 描述 (文本)
- 创建时间 (日期时间)
- 创建人 (字符串)
- 删除标识 (整数)

### 4. 合同与入住管理模块

#### 4.1 合同表 (elder_contract)
- 合同标识 (bigint)
- 合同编号 (字符串)
- 长者标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 合同开始日期 (日期)
- 合同结束日期 (日期)
- 押金 (浮点数)
- 付款方式 (整数)
- 签订日期 (日期)
- 合同文件路径 (字符串)
- 真实图片 (字符串)
- 状态 (整数)
- 终止日期 (日期)
- 终止原因 (文本)
- 备注 (文本)
- 创建时间 (日期时间)
- 更新时间 (日期时间)
- 删除标识 (整数)

#### 4.2 预订记录表 (elder_reservation)
- 预订标识 (bigint)
- 长者标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 联系人姓名 (字符串)
- 联系电话 (字符串)
- 房间标识 (bigint) - 外键关联房间表(elder_room.id)
- 预计入住日期 (日期时间)
- 预订日期 (日期时间)
- 押金 (浮点数)
- 状态 (整数)
- 备注 (文本)
- 创建时间 (日期时间)
- 更新时间 (日期时间)
- 创建人 (字符串)
- 更新人 (字符串)
- 删除标识 (整数)

#### 4.3 入住记录表 (elder_check_in)
- 主键 (bigint)
- 长者标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 合同标识 (bigint) - 外键关联合同表(elder_contract.id)
- 房间号 (字符串)
- 入住日期 (日期时间)
- 入住评估 (字符串)
- 随身物品 (文本)
- 备注 (文本)
- 入住状态 (整数)
- 入住金额 (浮点数)

#### 4.4 退住记录表 (elder_check_out)
- 主键 (bigint)
- 合同标识 (bigint) - 外键关联合同表(elder_contract.id)
- 长者标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 床位号 (字符串)
- 退住日期 (日期时间)
- 退住原因类型 (整数)
- 审批状态 (整数)
- 退费金额 (浮点数)
- 备注 (文本)

### 5. 咨询接待管理模块

#### 5.1 咨询接待记录表 (survey_consultation)
- 咨询记录标识 (bigint)
- 长者标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 咨询人姓名 (字符串)
- 咨询人电话 (字符串)
- 咨询目的 (字符串)
- 预计入住日期 (日期时间)
- 媒介渠道 (整数)
- 状态 (整数)
- 关闭或失效原因 (字符串)
- 签约日期 (日期时间)
- 备注 (文本)
- 真实图片 (字符串)
- 附件路径 (字符串)
- 创建时间 (日期时间)
- 更新时间 (日期时间)
- 创建人 (字符串)
- 删除标识 (整数)

### 6. 护理服务管理模块

#### 6.1 护理服务管理表 (elder_service_nursing)
- 主键标识 (bigint)
- 记录类型 (字符串)
- 护理计划内容 (文本)
- 护理项目名称 (字符串)
- 护理记录详情 (文本)
- 护理班次 (字符串)
- 护理等级 (整数)
- 护理费用 (浮点数)
- 护理变更人姓名 (字符串)
- 核查状态 (字符串)
- 生活记录内容 (文本)
- 护理人姓名 (字符串)
- 护理人岗位 (字符串)
- 护理小组名称 (字符串)
- 床位号 (整数)
- 护理知识库 (长文本)
- 创建时间 (日期时间)
- 更新时间 (日期时间)

#### 6.2 护理计划表 (elder_service_plan)
- 护理计划标识 (bigint)
- 长者标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 护工标识 (字符串)
- 计划内容 (字符串)
- 开始日期 (日期时间)
- 结束日期 (日期时间)
- 护理等级 (整数)
- 状态 (整数)

#### 6.3 长者生活服务记录表 (elder_service_living)
- 主键 (bigint)
- 老人标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 老人床位号 (字符串)
- 老人房间号 (字符串)
- 事故报告 (字符串)
- 探访人的名字 (字符串)
- 探访人员 (字符串)
- 探访人的事由 (字符串)
- 探访开始时间 (日期时间)
- 探访结束时间 (日期时间)
- 请假申请内容 (字符串)
- 请假原因 (字符串)
- 请假开始时间 (日期时间)
- 请假结束时间 (日期时间)
- 事故发生时间 (日期时间)
- 事故地点 (字符串)
- 事故描述 (文本)
- 严重程度 (字符串)

### 7. 财务管理模块

#### 7.1 账单表 (elder_bill)
- 主键标识 (bigint)
- 老人标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 房间标识 (int) - 外键关联房间表(elder_room.id)
- 账单编号 (字符串)
- 账单月份 (字符串)
- 费用类型 (整数)
- 缴费类型 (整数)
- 是否欠费 (整数)
- 费用 (浮点数)
- 账户余额 (浮点数)
- 总金额 (浮点数)
- 已付金额 (浮点数)
- 账单日期 (日期时间)
- 付款截止日期 (日期时间)
- 状态 (整数)
- 折扣 (浮点数)

### 8. 餐饮管理模块

#### 8.1 菜品分类表 (elder_dinner_category)
- 分类标识 (bigint)
- 分类名称 (字符串)
- 排序 (整数)
- 状态 (整数)
- 创建时间 (日期时间)

#### 8.2 用餐详细记录表 (elder_dinner_record)
- 主键 (bigint)
- 长者标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 菜名称或类型标识 (字符串)
- 餐别 (整数)
- 具体用餐时间 (日期时间)
- 就餐方式 (整数)
- 消费总金额 (浮点数)
- 备注 (字符串)

### 9. 设备管理模块

#### 9.1 APP用户设备表 (app_device)
- 主键标识 (bigint)
- 用户标识 (bigint) - 外键关联APP用户表(app_user.id)
- 设备类型标识 (int) - 外键关联设备类型表(app_device_category.device_type_id)
- 设备标识 (bigint)
- 设备唯一编码 (字符串)
- 状态是否禁用 (整数)
- 设备是否在线 (整数)
- 是否绑定 (整数)
- 经度 (字符串)
- 纬度 (字符串)
- 创建时间 (日期时间)
- 创建者 (字符串)

#### 9.2 设备类型表 (app_device_category)
- 主键标识 (bigint)
- 设备类型标识 (int)
- 老人标识 (bigint) - 外键关联APP用户表(app_user.id)
- 设备类型 (字符串)
- 排序值 (整数)
- 创建时间 (日期时间)
- 更新时间 (日期时间)
- 删除标识 (整数)

#### 9.3 设备报警表 (app_device_alarm)
- 主键标识 (bigint)
- 报警设备标识 (bigint) - 外键关联设备表(app_device.id)
- 用户标识 (bigint) - 外键关联APP用户表(app_user.id)
- 报警设备编号 (字符串)
- 设备报警信息 (字符串)
- 创建时间 (日期时间)
- 设备名称 (字符串)
- 消息处理状态 (整数)
- 解决时间 (日期时间)
- 处理人 (字符串)
- 处理内容 (字符串)
- 告警时间 (日期时间)
- 是否显示 (整数)

### 10. 消息通知模块

#### 10.1 消息通知表 (elder_message_notice)
- 主键 (bigint)
- 接收人标识 (bigint) - 外键关联老人信息表(elder_info.elder_id)
- 账单标识 (bigint) - 外键关联账单表(elder_bill.id)
- 房间标识 (bigint)
- 通知方式 (整数)
- 状态 (整数)
- 发送时间 (日期时间)
- 消息类型 (字符串)

### 11. 排班管理模块

#### 11.1 排班模板表 (scheduling_template)
- 主键标识 (bigint)
- 班次 (字符串)
- 交班人员 (字符串)
- 接班人员 (字符串)
- 是否巡更 (整数)

### 12. 系统管理模块

#### 12.1 角色信息表 (sys_role)
- 角色标识 (bigint)
- 角色名称 (字符串)
- 角色权限字符串 (字符串)
- 显示顺序 (整数)
- 数据范围 (整数)
- 菜单树选择项是否关联显示 (整数)
- 部门树选择项是否关联显示 (整数)
- 角色状态 (整数)
- 删除标志 (整数)
- 创建者 (字符串)
- 创建时间 (日期时间)
- 更新时间 (日期时间)
- 备注 (字符串)

#### 12.2 权限表 (sys_permission)
- 权限标识 (bigint)
- 父权限标识 (bigint)
- 权限名称 (字符串)
- 权限标识 (字符串)
- 路由地址 (字符串)
- 组件路径 (字符串)
- 类型 (整数)
- 图标 (字符串)
- 排序 (整数)
- 状态 (整数)
- 创建时间 (日期时间)
- 更新时间 (日期时间)
- 创建人 (字符串)
- 更新人 (字符串)
- 删除标识 (整数)

#### 12.3 用户角色关联表 (sys_user_role)
- 主键标识 (bigint)
- 用户标识 (bigint) - 外键关联用户表(sys_mgr_user.id)
- 角色标识 (bigint) - 外键关联角色表(sys_role.role_id)
- 创建时间 (日期时间)

#### 12.4 角色权限关联表 (sys_role_permission)
- 主键标识 (bigint)
- 角色标识 (bigint) - 外键关联角色表(sys_role.role_id)
- 权限标识 (bigint) - 外键关联权限表(sys_permission.id)
- 创建时间 (日期时间)

#### 12.5 字典类型表 (sys_dict_type)
- 字典主键 (bigint)
- 字典名称 (字符串)
- 字典类型 (字符串)
- 状态 (字符)
- 创建者 (字符串)
- 创建时间 (日期时间)
- 更新者 (字符串)
- 更新时间 (日期时间)
- 备注 (字符串)

#### 12.6 字典数据表 (sys_dict_properties)
- 字典编码 (bigint)
- 字典排序 (整数)
- 字典标签 (字符串)
- 字典键值 (字符串)
- 字典类型 (字符串)
- 样式属性 (字符串)
- 表格回显样式 (字符串)
- 是否默认 (字符)
- 状态 (字符)
- 创建者 (字符串)
- 创建时间 (日期时间)
- 更新者 (字符串)
- 更新时间 (日期时间)
- 备注 (字符串)

#### 12.7 系统操作日志表 (sys_log)
- 日志主键 (bigint)
- 模块标题 (字符串)
- 方法名称 (字符串)
- 方法中文名称或备注 (字符串)
- 请求方式 (字符串)
- 操作类别 (整数)
- 操作人员 (字符串)
- 部门名称 (字符串)
- 请求地址 (字符串)
- 主机地址 (字符串)
- 请求参数 (文本)
- 返回参数 (文本)
- 操作状态 (整数)
- 错误消息 (文本)
- 操作时间 (日期时间)
- 消耗时间 (bigint)

### 13. 库存管理模块

#### 13.1 库存表 (sys_stock)
- 主键标识 (bigint)
- 物品名字 (字符串)
- 物品序列号 (字符串)
- 批次号 (bigint)
- 条形码 (字符串)
- 入库原因 (整数)
- 出库原因 (整数)
- 库存数量 (整数)
- 领用部门 (字符串)
- 领用人 (字符串)
- 单价 (浮点数)
- 最近盘点日期 (日期)
- 库存状态 (整数)
- 供应商 (字符串)
- 供应商电话 (字符串)
- 供应商地址 (字符串)
- 仓库 (字符串)
- 操作人 (字符串)
- 创建时间 (日期时间)
- 入库时间 (日期时间)
- 出库时间 (日期时间)
- 删除标识 (整数)
