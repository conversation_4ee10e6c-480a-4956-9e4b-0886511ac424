- name: sql
  hosts: "{{ inventory }}"
  gather_facts: no
  tasks:
    - postgresql_query:
        login_host: 127.0.0.1
        db: "{{ db }}"
        login_password: "{{ login_password }}"
        port: "{{ pg_port | default(5432) | int }}"
        autocommit: yes # https://docs.ansible.com/ansible/2.9/modules/postgresql_query_module.html#parameters

        # query: ALTER TABLE public.bs_sys_site ADD CONSTRAINT ux_site__site_id UNIQUE (site_id);

        # query: > # get UNIQUE
        #   SELECT tc.constraint_name, tc.constraint_type, kcu.column_name
        #   FROM information_schema.table_constraints tc
        #   JOIN information_schema.key_column_usage kcu
        #   ON tc.constraint_name = kcu.constraint_name
        #   WHERE tc.table_schema = 'public'
        #   AND tc.table_name = 'bs_sys_site'
        #   AND tc.constraint_type = 'UNIQUE';

        # query: >
        #   ALTER TABLE "public"."bs_sys_site" 
        #     DROP CONSTRAINT "bs_sys_site_pkey",
        #     DROP COLUMN "id",
        #     ADD CONSTRAINT "bs_sys_site_pkey" PRIMARY KEY ("site_id");
        # query: >
        #   WITH CTE AS (
        #       SELECT
        #           id,
        #           ytqy_id,
        #           ROW_NUMBER() OVER (PARTITION BY ytqy_id ORDER BY id) AS RowNum
        #       FROM
        #           bs_sys_ytqy
        #   )
        #   DELETE FROM bs_sys_ytqy
        #   WHERE (ytqy_id, id) IN (SELECT ytqy_id, id FROM CTE WHERE RowNum > 1);
        # query: >
        #   ALTER TABLE "public"."bs_sys_ytqy" 
        #     DROP CONSTRAINT "bs_sys_ytqy_pkey",
        #     DROP COLUMN "id",
        #     ALTER COLUMN "ytqy_id" SET NOT NULL,
        #     ADD CONSTRAINT "bs_sys_ytqy_pkey" PRIMARY KEY ("ytqy_id");
        # query: >
        #   DELETE FROM bs_sys_road WHERE road_name='S122宁小线' OR road_name='G346沪安线' OR road_name='S239常淳线' OR road_name='G312线沪霍线' OR road_name='S232射宜线' OR road_name='S340虞宁线' OR road_name='S232射宜线' OR road_name='S357张丹线' OR road_name='S238镇常线' OR road_name='S504泰兴北-江南沿江公路';
        # query: >
        #   ALTER TABLE "public"."bs_sys_road" 
        #     DROP CONSTRAINT "bs_sys_road_pkey",
        #     DROP COLUMN "id",
        #     ALTER COLUMN "road_name" SET NOT NULL,
        #     ADD CONSTRAINT "bs_sys_road_pkey" PRIMARY KEY ("road_name");
        # query: >
        #   ALTER TABLE "public"."sms_log" 
        #     ALTER COLUMN "ytqy_id" TYPE varchar(64) USING "ytqy_id"::varchar(64);
        #   ALTER TABLE "public"."suspension" 
        #     ALTER COLUMN "ytqy_id" TYPE varchar(64) USING "ytqy_id"::varchar(64);
        #   ALTER TABLE "public"."unusual_log" 
        #     ALTER COLUMN "ytqy_id" TYPE varchar(64) USING "ytqy_id"::varchar(64);
      register: show
    - name: std
      debug: msg="{{ show.statusmessage }} {{ show.query_result }}"
