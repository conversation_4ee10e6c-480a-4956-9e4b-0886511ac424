- name: update illegal
  hosts: "{{ inventory }}"
  gather_facts: no
  tasks:
    - name: UPDATE ILLEGAL
      postgresql_query:
        login_host: 127.0.0.1
        db: "{{ db }}"
        login_password: "{{ login_password }}"
        port: "{{ pg_port | default(5432) | int }}"
        query: |
          UPDATE illegal set description = '{{ item.value }}' WHERE gb_code = '{{ item.key }}';
          INSERT INTO illegal(id, gb_code, description)
          SELECT nextval('sequence_generator'), '{{ item.key }}', '{{ item.value }}'
          WHERE NOT EXISTS (
            SELECT gb_code FROM illegal WHERE gb_code = '{{ item.key }}'
          );
      loop:
        - { key: '0', value:	'正常' }
        - { key: '1373', value:	'逆行' }
        - { key: '1625', value:	'违反信号灯通行' }
        - { key: '1117', value:	'压线' }
        - { key: '6095', value:	'不按导向' }
        - { key: '1018', value:	'机占非' }
        - { key: '1019', value:	'占用专用车道' }
        - { key: '1116', value:	'大车占道' }
        - { key: '6097', value:	'路口滞留' }
        - { key: '1128', value:	'超速' }
        - { key: '1130', value:	'超速' }
        - { key: '6093', value:	'超速' }
        - { key: '1628', value:	'超速' }
        - { key: '1629', value:	'超速' }
        - { key: '4609', value:	'超速' }
        - { key: '4610', value:	'超速' }
        - { key: '1632', value:	'超速' }
        - { key: '1633', value:	'超速' }
        - { key: '1369', value:	'超速' }
        - { key: '1722', value:	'超速' }
        - { key: '1723', value:	'超速' }
        - { key: '4706', value:	'超速' }
        - { key: '4707', value:	'超速' }
        - { key: '1650', value:	'超速' }
        - { key: '1914', value:	'超速' }
        - { key: '1916', value:	'超速' }
        - { key: '4710', value:	'超速' }
        - { key: '4711', value:	'超速' }
        - { key: '16251', value:	'绿灯停车' }
        - { key: '6090', value:	'路口停车' }
        - { key: '4306', value:	'低速' }
        - { key: '6046', value:	'超速' }
        - { key: '6047', value:	'超速' }
        - { key: '6048', value:	'超速' }
        - { key: '6094', value:	'超速' }
        - { key: '4016', value:	'低速' }
        - { key: '1622', value:	'占用应急车道' }
        - { key: '1120', value:	'未系安全带' }
        - { key: '1121', value:	'违章掉头' }
        - { key: '1357', value:	'不礼让行人' }
        - { key: '3001', value:	'行人闯红灯' }
        - { key: '1313', value:	'左转不让直行' }
        - { key: '1314', value:	'右转不让左转' }
        - { key: '1123', value:	'掉头不让直行' }
        - { key: '6088', value:	'大弯小转' }
        - { key: '1362', value:	'打电话' }
        - { key: '2013', value:	'非机动车载人' }
        - { key: '12280', value:	'闯绿灯' }
        - { key: '2006', value:	'占用机动车道' }
        - { key: '1119', value:	'未戴头盔' }
        - { key: '2004', value:	'非机动车逆行' }
        - { key: '151004', value:	'越线' }
        - { key: '1025', value:	'违法停车' }
        - { key: '1371', value:	'吸烟' }
        - { key: '3019', value:	'副驾驶未系安全带' }
        - { key: '1240', value:	'主驾驶不规范系安全带' }
        - { key: '1316', value:	'不让右方来车先行' }
        - { key: '4006', value:	'匝道不让主路先行' }
        - { key: '1312', value:	'环形路口未让行' }
        - { key: '1224', value:	'行人预警' }
        - { key: '1225', value:	'路障' }
        - { key: '1226', value:	'施工' }
        - { key: '1233', value:	'抛撒物' }
        - { key: '1043', value:	'蛇形行驶' }
        - { key: '1356', value:	'斑马线未减速' }
        - { key: '1094', value:	'未保持车距' }
        - { key: '1232', value:	'连续变道' }
        - { key: '1304', value:	'右侧超车' }
        - { key: '1231', value:	'飙车' }
        - { key: '1234', value:	'紧急制动' }
        - { key: '2022', value:	'超温' }
        - { key: '2854', value:	'未按专用车道行驶' }
        - { key: '2012', value:	'非机动车超速' }
        - { key: '13440', value:	'大车右转未停车' }
        - { key: '4312', value:	'大货车占超车道' }
        - { key: '9999', value:	'雾检测' }