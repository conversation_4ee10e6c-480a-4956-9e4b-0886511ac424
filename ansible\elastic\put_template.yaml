# ap elastic/put_template -t platform,hub,verify,subtoct,checks,ytqy -e="host=city_elastic"
# ap elastic/put_template -t platform,mono,verify,hub,toledo,hik,nova -e="host=jintan_elastic"
# ap elastic/put_template -t platform,mono,verify,hub,toledo,hik,nova -e="host=liyang_elastic"
# ap elastic/put_template -t platform,mono,verify,hub,toledo,hik,nova -e="host=wujin"
# ap elastic/put_template -t platform,mono,verify,hub,toledo,hik,nova -e="host=xinbei"
# ap elastic/put_template -t platform,hub,verify -e="host=points_taizhou"
# ap elastic/put_template -t platform,hub,verify -e="host=p21,p22"

- name: Put platform
  hosts: "{{ host }}"
  tags: [platform]
  vars_files:
    - common_vars.yaml
  tasks:
    - include_tasks: common_tasks.yaml
  
    - name: put platform_template
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_template/platform_template {{ headers }} \
        -d '{ "index_patterns": ["platform-*"], "settings": { "index.lifecycle.name": "delete_policy_90d" }}'
      register: platform_result
    - debug: msg="{{ platform_result.cmd }} {{ platform_result.stdout }} {{ platform_result.stderr }}"

    - name: put platform indexs
      shell: > 
        curl -X PUT http://127.0.0.1:9200/platform-*/_settings {{ headers }} \
        -d '{ "index": { "lifecycle": { "name": "delete_policy_90d" } } }'
      register: platform_result2
    - debug: msg="{{ platform_result2.cmd }} {{ platform_result2.stdout }} {{ platform_result2.stderr }}"

- name: Put Hub
  hosts: "{{ host }}"
  tags: [hub]
  vars_files:
    - common_vars.yaml
  tasks:
    - include_tasks: common_tasks.yaml
  
    - name: put flow_hub_template
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_template/flow_hub_template {{ headers }} \
        -d '{ "index_patterns": ["flow-hub-*"], "settings": { "index.lifecycle.name": "delete_policy_60d" }}'
      register: hub_result
    - debug: msg="{{ hub_result.cmd }} {{ hub_result.stdout }} {{ hub_result.stderr }}"

    - name: put flow-hub indexs
      shell: > 
        curl -X PUT http://127.0.0.1:9200/flow-hub-*/_settings {{ headers }} \
        -d '{ "index": { "lifecycle": { "name": "delete_policy_60d" } } }'
      register: hub_result2
    - debug: msg="{{ hub_result2.cmd }} {{ hub_result2.stdout }} {{ hub_result2.stderr }}"

- name: Put Verify
  hosts: "{{ host }}"
  tags: [verify]
  vars_files:
    - common_vars.yaml
  tasks:
    - include_tasks: common_tasks.yaml
  
    - name: put flow_verify_template
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_template/flow_verify_template {{ headers }} \
        -d '{ "index_patterns": ["flow-verify-*"], "settings": { "index.lifecycle.name": "delete_policy_60d" }}'
      register: verify_result
    - debug: msg="{{ verify_result.cmd }} {{ verify_result.stdout }} {{ verify_result.stderr }}"

    - name: put flow-verify indexs
      shell: > 
        curl -X PUT http://127.0.0.1:9200/flow-verify-*/_settings  {{ headers }} \
        -d '{ "index": { "lifecycle": { "name": "delete_policy_60d" } } }'
      register: verify_result2
    - debug: msg="{{ verify_result2.cmd }} {{ verify_result2.stdout }} {{ verify_result2.stderr }}"
      
- name: Put subtoct
  hosts: "{{ host }}"
  tags: [subtoct]
  vars_files:
    - common_vars.yaml
  tasks:
    - include_tasks: common_tasks.yaml
  
    - name: put subtoct_template
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_template/subtoct_template {{ headers }} \
        -d '{ "index_patterns": ["subtoct-*"], "settings": { "index.lifecycle.name": "delete_policy_60d" }}'
      register: subtoct_result
    - debug: msg="{{ subtoct_result.cmd }} {{ subtoct_result.stdout }} {{ subtoct_result.stderr }}"

    - name: put subtoct indexs
      shell: > 
        curl -X PUT http://127.0.0.1:9200/subtoct-*/_settings {{ headers }} \
        -d '{ "index": { "lifecycle": { "name": "delete_policy_60d" } } }'
      register: subtoct_result2
    - debug: msg="{{ subtoct_result2.cmd }} {{ subtoct_result2.stdout }} {{ subtoct_result2.stderr }}"
      
- name: Put checks
  hosts: "{{ host }}"
  tags: [checks]
  vars_files:
    - common_vars.yaml
  tasks:
    - include_tasks: common_tasks.yaml
  
    - name: put checks_template
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_template/checks_template {{ headers }} \
        -d '{ "index_patterns": ["checks-*"], "settings": { "index.lifecycle.name": "delete_policy_30d" }}'
      register: checks_result
    - debug: msg="{{ checks_result.cmd }} {{ checks_result.stdout }} {{ checks_result.stderr }}"

    - name: put subtoct indexs
      shell: > 
        curl -X PUT http://127.0.0.1:9200/checks-*/_settings {{ headers }} \
        -d '{ "index": { "lifecycle": { "name": "delete_policy_30d" } } }'
      register: checks_result2
    - debug: msg="{{ checks_result2.cmd }} {{ checks_result2.stdout }} {{ checks_result2.stderr }}"
      
- name: Put ytqy
  hosts: "{{ host }}"
  tags: [ytqy]
  vars_files:
    - common_vars.yaml
  tasks:
    - include_tasks: common_tasks.yaml

    - name: put ytqy_template
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_template/ytqy_template {{ headers }} \
        -d '{ "index_patterns": ["czmp-ytqy-*"], "settings": { "index.lifecycle.name": "delete_policy_30d" }}'
      register: ytqy_result
    - debug: msg="{{ ytqy_result.cmd }} {{ ytqy_result.stdout }} {{ ytqy_result.stderr }}"

    - name: put subtoct indexs
      shell: > 
        curl -X PUT http://127.0.0.1:9200/czmp-ytqy-*/_settings {{ headers }} \
        -d '{ "index": { "lifecycle": { "name": "delete_policy_30d" } } }'
      register: ytqy_result2
    - debug: msg="{{ ytqy_result2.cmd }} {{ ytqy_result2.stdout }} {{ ytqy_result2.stderr }}"

- name: Put mono
  hosts: "{{ host }}"
  tags: [mono]
  vars_files:
    - common_vars.yaml
  tasks:
    - include_tasks: common_tasks.yaml

    - name: put mono_template
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_template/mono_template {{ headers }} \
        -d '{ "index_patterns": ["flow-mono-*"], "settings": { "index.lifecycle.name": "delete_policy_60d" }}'
      register: mono_result
    - debug: msg="{{ mono_result.cmd }} {{ mono_result.stdout }} {{ mono_result.stderr }}"

    - name: put mono indexs
      shell: > 
        curl -X PUT http://127.0.0.1:9200/flow-mono-*/_settings {{ headers }} \
        -d '{ "index": { "lifecycle": { "name": "delete_policy_60d" } } }'
      register: mono_result2
    - debug: msg="{{ mono_result2.cmd }} {{ mono_result2.stdout }} {{ mono_result2.stderr }}"

- name: Put nova
  hosts: "{{ host }}"
  tags: [nova]
  vars_files:
    - common_vars.yaml
  tasks:
    - include_tasks: common_tasks.yaml

    - name: put nova_template
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_template/nova_template {{ headers }} \
        -d '{ "index_patterns": ["nova-driver-*"], "settings": { "index.lifecycle.name": "delete_policy_30d" }}'
      register: nova_result
    - debug: msg="{{ nova_result.cmd }} {{ nova_result.stdout }} {{ nova_result.stderr }}"

    - name: put nova indexs
      shell: > 
        curl -X PUT http://127.0.0.1:9200/nova-driver-*/_settings {{ headers }} \
        -d '{ "index": { "lifecycle": { "name": "delete_policy_30d" } } }'
      register: nova_result2
    - debug: msg="{{ nova_result2.cmd }} {{ nova_result2.stdout }} {{ nova_result2.stderr }}"
      
- name: Put hik
  hosts: "{{ host }}"
  tags: [hik]
  vars_files:
    - common_vars.yaml
  tasks:
    - include_tasks: common_tasks.yaml

    - name: put hik_template
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_template/hik_template {{ headers }} \
        -d '{ "index_patterns": ["hik-driver-*"], "settings": { "index.lifecycle.name": "delete_policy_30d" }}'
      register: hik_result
    - debug: msg="{{ hik_result.cmd }} {{ hik_result.stdout }} {{ hik_result.stderr }}"

    - name: put hik indexs
      shell: > 
        curl -X PUT http://127.0.0.1:9200/hik-driver-*/_settings {{ headers }} \
        -d '{ "index": { "lifecycle": { "name": "delete_policy_30d" } } }'
      register: hik_result2
    - debug: msg="{{ hik_result2.cmd }} {{ hik_result2.stdout }} {{ hik_result2.stderr }}"
      
- name: Put toledo
  hosts: "{{ host }}"
  tags: [toledo]
  vars_files:
    - common_vars.yaml
  tasks:
    - include_tasks: common_tasks.yaml

    - name: put toledo_template
      shell: > 
        curl -X PUT http://127.0.0.1:9200/_template/toledo_template {{ headers }} \
        -d '{ "index_patterns": ["toledo-driver-*"], "settings": { "index.lifecycle.name": "delete_policy_30d" }}'
      register: toledo_result
    - debug: msg="{{ toledo_result.cmd }} {{ toledo_result.stdout }} {{ toledo_result.stderr }}"

    - name: put toledo indexs
      shell: > 
        curl -X PUT http://127.0.0.1:9200/toledo-driver-*/_settings {{ headers }} \
        -d '{ "index": { "lifecycle": { "name": "delete_policy_30d" } } }'
      register: toledo_result2
    - debug: msg="{{ toledo_result2.cmd }} {{ toledo_result2.stdout }} {{ toledo_result2.stderr }}"