# ap tunnel -K --extra-vars="host=y0"
# ap tunnel -K --extra-vars="host=p14,home=home/xbzc"
# ap tunnel -K --extra-vars="host=xb,home=root"

- name: Update cloudflare tunnel
  hosts: "{{ host }}"
  become: yes
  environment:
    TUNNEL_ORIGIN_CERT: /{{ home | default('home/czmp') }}/.cloudflared/cert.pem # become will switch user to root
  tasks:
    - name: config directory
      file:
        path: /etc/cloudflared
        state: directory
    - name: config.yml
      copy:
        src: "../tunnels/{{ host }}-config.yml"
        dest: /etc/cloudflared/config.yml
    - name: restart service
      shell: |
        systemctl restart cloudflared
    - name: route script
      script: "../tunnels/{{ host }}-config.sh"
      register: show
    - name: std
      debug: msg="{{ show.stderr }} {{ show.stdout }}"