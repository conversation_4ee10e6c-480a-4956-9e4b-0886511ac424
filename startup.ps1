# https://devblogs.microsoft.com/scripting/use-powershell-to-create-job-that-runs-at-startup/#create-the-job-trigger
# https://www.pstips.net/about-scheduledjob.html



Unregister-ScheduledJob SmartOilboxJava
Unregister-ScheduledJob SmartOilboxFront
Unregister-ScheduledJob SmartOilboxAdmin

# 不能用 -AtLogOn，某些后台进程注销后不会被中止
$trigger = New-JobTrigger -AtStartup -RandomDelay 00:00:30
Register-ScheduledJob -Trigger $trigger -FilePath C:\work\socode.operation\airwheel\java.ps1 -Name SmartOilboxJava
Register-ScheduledJob -Trigger $trigger -FilePath C:\work\socode.operation\airwheel\front.ps1 -Name SmartOilboxFront
Register-ScheduledJob -Trigger $trigger -FilePath C:\work\socode.operation\airwheel\admin.ps1 -Name SmartOilboxAdmin
# Register-ScheduledJob -Trigger $trigger -FilePath C:\work\socode.operation\airwheel\winlogbeat.ps1 -Name WinlogBeat

# Start-Job, Get-Job, Stop-Job, and Receive-Job
# taskschd.msc:Microsoft\Windows\PowerShell\ScheduledJob

# PS C:\> Get-Command -Module PSScheduledJob
# CommandType Name                    ModuleName
# ----------- ----                    ----------
# Cmdlet      Add-JobTrigger          PSScheduledJob
# Cmdlet      Disable-JobTrigger      PSScheduledJob
# Cmdlet      Disable-ScheduledJob    PSScheduledJob
# Cmdlet      Enable-JobTrigger       PSScheduledJob
# Cmdlet      Enable-ScheduledJob     PSScheduledJob
# Cmdlet      Get-JobTrigger          PSScheduledJob
# Cmdlet      Get-ScheduledJob        PSScheduledJob
# Cmdlet      Get-ScheduledJobOption  PSScheduledJob
# Cmdlet      New-JobTrigger          PSScheduledJob
# Cmdlet      New-ScheduledJobOption  PSScheduledJob
# Cmdlet      Register-ScheduledJob   PSScheduledJob
# Cmdlet      Remove-JobTrigger       PSScheduledJob
# Cmdlet      Set-JobTrigger          PSScheduledJob
# Cmdlet      Set-ScheduledJob        PSScheduledJob
# Cmdlet      Set-ScheduledJobOption  PSScheduledJob
# Cmdlet      Unregister-ScheduledJob PSScheduledJob

# $C = Get-Culture | Select-Object -Property *
# Out-File -InputObject $C -Width 100 -FilePath d:\test.txt

# $trigger = New-JobTrigger -AtLogOn -RandomDelay 00:00:10
# Register-ScheduledJob -Trigger $trigger -FilePath d:\work\socode.operation\airwheel\test.ps1 -Name Test

Get-ScheduledJob 1 | Get-JobTrigger
Get-JobTrigger 1