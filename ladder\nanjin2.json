{"log": {"loglevel": "warning", "access": "/var/log/v2ray/access.log", "error": "/var/log/v2ray/error.log"}, "inbounds": [{"port": 433, "listen": "0.0.0.0", "protocol": "socks", "settings": {"auth": "password", "accounts": [{"user": "zcijin", "pass": "pidanxiong"}], "udp": true}}], "outbounds": [{"protocol": "vmess", "settings": {"vnext": [{"address": "tokyo.windcss.com", "port": 433, "users": [{"security": "none", "id": "08ea6cfc-c76b-11ec-9d64-0242ac120003", "alterId": 64}]}]}, "mux": {"enabled": false, "concurrency": 8}, "streamSettings": {"security": "tls", "network": "ws", "tlsSettings": {"allowInsecure": false}}}]}