# ## Configure 'ip' variable to bind kubernetes services on a
# ## different ip than the default iface
# ## We should set etcd_member_name for etcd cluster. The node that is not a etcd member do not need to set the value, or can set the empty string value.
[all]
y0 ansible_host=y0 ip=************* etcd_member_name=etcd0
y1 ansible_host=y1 ip=************* etcd_member_name=etcd1
y2 ansible_host=y2 ip=************* etcd_member_name=etcd2
y3 ansible_host=y3 ip=************* etcd_member_name=etcd3
y4 ansible_host=y4 ip=************* etcd_member_name=etcd4
y5 ansible_host=y5 ip=************* etcd_member_name=etcd5
y6 ansible_host=y6 ip=************* etcd_member_name=etcd6

# ## configure a bastion host if your nodes are not directly reachable
# [bastion]
# bastion ansible_host=x.x.x.x ansible_user=some_user

[kube_control_plane]
y0

[etcd]
y0

[kube_node]
y1
y2
y3
y4
y5
y6

[calico_rr]

[k8s_cluster:children]
kube_control_plane
kube_node
calico_rr
