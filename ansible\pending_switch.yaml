# ap switch_pending -e="faster=true"
# ap switch_pending -e="disable=true"
# ap switch_pending -e="withOutCache=true"

- name: switch
  hosts: y0
  gather_facts: no
  tasks:
    - name: request
      uri:
        # url: http://{{ item.host }}:30000/api/switch-pending-faster-default?faster={{ faster }}
        url: "http://{{ item.host }}:30000/api/switch-pending-faster-disable?disable={{ disable }}"
        # url: "http://{{ item.host }}:30000/api/switch-pending-without-cache?withOutCache={{ withOutCache }}"
        method: POST
      loop:
        - { host: "*************" }
        - { host: "*************" }
        - { host: "*************" }
        - { host: "*************" }
        - { host: "*************" }
