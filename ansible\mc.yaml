# ap mc -e="host=m1 source=yminio target=liyang date=230320 no=32048101000220230320213931000000"

- name: mc cp
  hosts: "{{ host }}"
  gather_facts: no
  tasks:
    - name: shell
      shell: /home/<USER>/minio-binaries/mc cp {{ source }}/{{ item.ftype }}/{{ date }}/{{ no }}-{{ item.mtype }} {{ target }}/{{ item.ftype }}/{{ date }}/
      loop:
        - { ftype: "videos", mtype: "31.mp4"  }
        - { ftype: "videos", mtype: "32.mp4"  }
        - { ftype: "images", mtype: "70.jpeg"  }
        - { ftype: "images", mtype: "75.jpeg"  }
        - { ftype: "images", mtype: "77.jpeg"  }
        - { ftype: "images", mtype: "11i.jpeg"  }
        - { ftype: "images", mtype: "12i.jpeg"  }
        - { ftype: "images", mtype: "13i.jpeg"  }
        - { ftype: "images", mtype: "14i.jpeg"  }
        - { ftype: "images", mtype: "15i.jpeg"  }
      register: output
    - name: Show results
      ansible.builtin.debug:
        var: output
