# cp config.fish ~/.config/fish/config.fish
alias fconfig 'cp /Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts/config.fish ~/.config/fish/config.fish'

source ~/.config/fish/vibe.fish

eval "$(/opt/homebrew/bin/brew shellenv)"
if status is-interactive
    # Commands to run in interactive sessions can go here
end

set -Ux JAVA_HOME /opt/homebrew/opt/openjdk@17
set -Ux ANDROID_HOME $HOME/Library/Android/sdk
set -Ux SPARK_HOME $HOME/work/spark
set -Ux HF_ENDPOINT https://hf-mirror.com

fish_add_path /usr/local/bin
fish_add_path /opt/homebrew/bin
fish_add_path /opt/homebrew/opt/openjdk@21/bin
fish_add_path /opt/homebrew/opt/ruby@2.7/bin
fish_add_path $HOME/.local/bin
fish_add_path $HOME/.cargo/bin
fish_add_path $HOME/work/flutter
fish_add_path $HOME/Library/Application Support/Coursier/bin
fish_add_path $ANDROID_HOME/emulator
fish_add_path $ANDROID_HOME/platform-tools

alias unset 'set --erase' # fish unsupport 'unset'
alias proxyon="export http_proxy='http://127.0.0.1:1087';export https_proxy='http://127.0.0.1:1087'"
alias proxyoff="unset http_proxy;unset https_proxy"
alias vdl="yt-dlp --cookies-from-browser edge"
alias vdlp="yt-dlp --cookies-from-browser edge --proxy socks5://127.0.0.1:7875"
alias rrclone="rclone sync -i --exclude '.DS_Store'"

alias gs='git switch'
alias gpp='git push && git push ali && pm2 deploy production'
alias gcp='git com && git push azure'

alias dlog='docker logs -fn 200'
function dbash; docker exec -it $argv /bin/bash; end
function dash; docker exec -it $argv /bin/sh; end # https://stackoverflow.com/a/44804509/346701
function ll; ls -l $argv; end
function ya
	set tmp (mktemp -t "yazi-cwd.XXXXX")
	yazi $argv --cwd-file="$tmp"
	if set cwd (cat -- "$tmp"); and [ -n "$cwd" ]; and [ "$cwd" != "$PWD" ]
		cd -- "$cwd"
	end
	rm -f -- "$tmp"
end

set ansiblePath $HOME/Library/CloudStorage/OneDrive-个人/scripts/ansible
function ap --argument-names playbook option1 option2 option3 option4
    ansible-playbook -i $ansiblePath/inventory.yaml $ansiblePath/$playbook.yaml $option1 $option2 $option3 $option4
end

function mn; ssh mn; end
function tk; ssh tk; end
function scp_tk; scp -o ProxyCommand="ssh nj -W tk" tk; end
function sg; ssh sg; end
function nj; ssh nj; end
function of; ssh of; end

function y0; ssh y0; end
# function y1; ssh -o ProxyCommand="ssh -p 15600 <EMAIL>:15600 -W *************:1122" czmp@*************; end
function y1; ssh y1; end
function y2; ssh y2; end
function y3; ssh y3; end
function y4; ssh y4; end
function y5; ssh y5; end
function y6; ssh y6; end
function m1; ssh m1; end
function m2; ssh m2; end
function m3; ssh m3; end

# function j1; ssh -p 15600 <EMAIL>:15600; end
function j1; ssh j1; end
function j2; ssh j2; end
function j3; ssh j3; end
function l1; ssh l1; end
function l2; ssh l2; end
function l3; ssh l3; end
function l4; ssh l4; end
function lo1; ssh lo1; end
function lo2; ssh lo2; end
function wj; ssh wj; end
function xb; ssh xb; end

function p1; ssh p1; end
function p2; ssh p2; end
function p3; ssh p3; end
function p4; ssh p4; end
function p75; ssh p75; end
function p5; ssh p5; end
function p6; ssh p6; end
function p7; ssh p7; end
function p8; ssh p8; end
function p11; ssh p11; end
function p12; ssh p12; end
function p13; ssh p13; end
function p14; ssh p14; end
function p15; ssh p15; end

function p20; ssh p20; end
function p21; ssh p21; end
function p22; ssh p22; end
function p23; ssh p23; end
function p26; ssh p26; end
function p27; ssh p27; end
function p30; ssh p30; end
function p31; ssh p31; end
function p32; ssh p32; end
function p40; ssh p40; end
function p41; ssh p41; end
function fm; ssh fm; end
function fmc; ssh fmc; end
function zt; ssh zt; end

# pnpm
set -gx PNPM_HOME $HOME/Library/pnpm
if not string match -q -- $PNPM_HOME $PATH
  set -gx PATH "$PNPM_HOME" $PATH
end
# pnpm end
# ~/.zshrc: export PATH="/Users/<USER>/Library/pnpm:$PATH"

# https://github.com/starship/starship
# Some fonts don't support its icons, 'Fira Code' is recommended.
starship init fish | source

pyenv init - | source

# bun
set --export BUN_INSTALL "$HOME/.bun"
set --export PATH $BUN_INSTALL/bin $PATH

# https://vninja.net/2024/12/28/ghostty-workaround-for-missing-or-unsuitable-terminal-xterm-ghostty/
if test "$TERM_PROGRAM" = "ghostty"
    set -gx TERM xterm-256color
end
