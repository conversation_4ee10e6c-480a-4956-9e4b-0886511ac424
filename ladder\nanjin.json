{"log": {"loglevel": "warning", "access": "/var/log/v2ray/access.log", "error": "/var/log/v2ray/error.log"}, "inbounds": [{"listen": "0.0.0.0", "port": 433, "protocol": "vmess", "settings": {"clients": [{"id": "08ea6cfc-c76b-11ec-9d64-0242ac120004", "alterId": 64}]}, "streamSettings": {"network": "ws", "security": "tls", "tlsSettings": {"certificates": [{"certificateFile": "/var/www/fullchain.cer", "keyFile": "/var/www/nj.windcss.com.key"}]}}}], "outbounds": [{"protocol": "vmess", "settings": {"vnext": [{"address": "tokyo.windcss.com", "port": 433, "users": [{"security": "none", "id": "08ea6cfc-c76b-11ec-9d64-0242ac120003", "alterId": 64}]}]}, "mux": {"enabled": false, "concurrency": 8}, "streamSettings": {"security": "tls", "network": "ws", "tlsSettings": {"allowInsecure": false}}}]}