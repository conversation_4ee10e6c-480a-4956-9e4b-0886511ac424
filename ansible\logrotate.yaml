# ansible-galaxy collection install ericsysmin.system
# ap logrotate -K --extra-vars="host=t1"

- name: logrotate
  hosts: "{{ host }}"
  become: yes
  roles:
    - role: ericsysmin.system.logrotate
      logrotate_files:
        - name: docker
          path: "/var/lib/docker/containers/*/*-json.log"
          options:
            - daily
            - size 25M
            - missingok
            - compress
            - delaycompress
            - copytruncate

# multiple wildcard entries
# https://superuser.com/questions/255951/logrotate-configuration-file-syntax-multiple-wildcard-entries-possible