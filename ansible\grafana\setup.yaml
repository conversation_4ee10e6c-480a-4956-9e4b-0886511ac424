# ap grafana/setup -K --extra-vars="host=p30"
# ap grafana/setup -K --extra-vars="host=p14 user=xbzc"

- name: Grafana Setup
  hosts: "{{ host }}"
  become: yes
  become_user: "{{ user | default('czmp') }}"
  gather_facts: yes
  tasks:
    - name: debug through ansible.env
      debug: var=ansible_env.HOME # ansible_env need gather_facts
    - name: standby directory
      file:
        path: ~/grafana # equal
        # or: path: "{{ ansible_env.HOME }}/grafana"
        state: directory
        mode: "0755"
    - name: standby data directory
      file:
        path: "{{ data_root | default('/mnt/data/grafana') }}"
        state: directory
        mode: "0777"
    - name: compose down
      community.docker.docker_compose_v2:
        state: absent
        project_src: ~/grafana
      ignore_errors: true
    - name: copy .env
      copy:
        src: .env
        dest: ~/grafana/.env
        mode: "0755"
    - name: copy grafana.ini
      copy:
        src: grafana11.ini
        dest: ~/grafana/grafana.ini
        mode: "0755"
    - name: copy docker-compose.yml
      copy:
        src: ./grafana.yml
        dest: ~/grafana/docker-compose.yml
        mode: "0755"
    - name: compose up
      community.docker.docker_compose_v2:
        state: present
        project_src: ~/grafana
      register: output
    - name: Show results
      ansible.builtin.debug:
        var: output