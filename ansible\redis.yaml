# ap redis --extra-vars="host=points_wujin"

- name: Redis
  hosts: "{{ host }}"
  tasks:
    - name: Flush
      community.general.redis:
        login_password: CZMP.2019
        command: flush
        db: 0
        flush_mode: db
        # flush_mode: all


# https://docs.ansible.com/ansible/latest/collections/community/general/redis_module.html
# ansible-galaxy collection install community.general
# client: pip3 install redis
  # if OpenSSL/crypto.py error:
  # sudo rm -rf /usr/lib/python3/dist-packages/OpenSSL
  # pip3 install pyOpenSSL --upgrade