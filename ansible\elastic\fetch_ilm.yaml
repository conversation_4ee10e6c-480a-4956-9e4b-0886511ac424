# ap elastic/fetch_ilm -e="host=city_elastic"
# ap elastic/fetch_ilm -e="host=jintan_elastic"
# ap elastic/fetch_ilm -e="host=liyang_elastic"
# ap elastic/fetch_ilm -e="host=wujin"
# ap elastic/fetch_ilm -e="host=xinbei"
# ap elastic/fetch_ilm -e="host=points_taizhou"
# ap elastic/fetch_ilm -e="host=p21,p22"
# ap elastic/fetch_ilm -e="host=city_elastic,jintan_elastic,liyang_elastic,wujin,xinbei,points_taizhou,p21,p22"

- name: Fetch ilm
  hosts: "{{ host }}"
  vars_files:
    - common_vars.yaml
  
  tasks:
    - include_tasks: common_tasks.yaml
  
    - name: curl _ilm/policy
      shell: "curl -u elastic:{{ pwd }} -X GET 'http://127.0.0.1:9200/_ilm/policy'"
      register: result

    - name: Save delete_policy_30d
      copy:
        content: "{{ result.stdout | from_json | json_query('delete_policy_30d') | to_nice_json }}"
        dest: /tmp/es_ilm_30d.json

    - name: fetch _ilm 30d
      fetch:
        src: /tmp/es_ilm_30d.json
        dest: ~/work/fetch_data/es_ilms/{{ inventory_hostname }}_30d.json
        flat: true
  
    - name: Save delete_policy_60d
      copy:
        content: "{{ result.stdout | from_json | json_query('delete_policy_60d') | to_nice_json }}"
        dest: /tmp/es_ilm_60d.json

    - name: fetch _ilm 60d
      fetch:
        src: /tmp/es_ilm_60d.json
        dest: ~/work/fetch_data/es_ilms/{{ inventory_hostname }}_60d.json
        flat: true

    - name: Save delete_policy_90d
      copy:
        content: "{{ result.stdout | from_json | json_query('delete_policy_90d') | to_nice_json }}"
        dest: /tmp/es_ilm_90d.json

    - name: fetch _ilm 90d
      fetch:
        src: /tmp/es_ilm_90d.json
        dest: ~/work/fetch_data/es_ilms/{{ inventory_hostname }}_90d.json
        flat: true
