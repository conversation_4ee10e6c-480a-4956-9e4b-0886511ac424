tunnel: e98cd8f0-b989-42be-9083-49c7b39755ad
credentials-file: /home/<USER>/.cloudflared/e98cd8f0-b989-42be-9083-49c7b39755ad.json
protocol: auto
ingress:
  - hostname: p4-ssh.socode.top
    service: ssh://localhost:22
  - hostname: p4-api.socode.top
    service: http://localhost:80
  - hostname: p4-hub.socode.top
    service: http://localhost:9133
  - hostname: p4-kibana.socode.top
    service: http://localhost:5601
  - hostname: p4-kafka-87.socode.top
    service: http://localhost:8087
  - hostname: p4-pg.socode.top
    service: tcp://localhost:5432
  - hostname: p4-clickhouse.socode.top
    service: http://localhost:8123
  - hostname: p4-minio.socode.top
    service: http://localhost:9000
  - hostname: p4-minio-console.socode.top
    service: http://localhost:9001
  - hostname: p4-grafana.socode.top
    service: http://localhost:3080
  - hostname: p4-web.socode.top
    service: http://localhost:16002
  - service: http_status:404

# cloudflared tunnel route dns p4 p4-ssh.socode.top
# cloudflared tunnel route dns p4 p4-api.socode.top
# cloudflared tunnel route dns p4 p4-hub.socode.top
# cloudflared tunnel route dns p4 p4-web.socode.top
# cloudflared tunnel route dns p4 p4-kibana.socode.top
# cloudflared tunnel route dns p4 p4-kafka-87.socode.top
# cloudflared tunnel route dns p4 p4-pg.socode.top
# cloudflared tunnel route dns p4 p4-clickhouse.socode.top
# cloudflared tunnel route dns p4 p4-minio.socode.top
# cloudflared tunnel route dns p4 p4-minio-console.socode.top
# cloudflared tunnel route dns p4 p4-grafana.socode.top