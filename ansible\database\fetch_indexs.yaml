# ap database/entry -t city --extra-vars="play=fetch_indexs"
# ap database/entry -t area --extra-vars="play=fetch_indexs"
# ap database/entry -t points --extra-vars="play=fetch_indexs"

- name: Fetch Indexs
  hosts: "{{ inventory }}"
  gather_facts: no
  tasks:
    - postgresql_query:
        login_host: 127.0.0.1
        db: "{{ db }}"
        login_password: "{{ login_password }}"
        port: "{{ pg_port | default(5432) | int }}"
        autocommit: yes
        query: >
          SELECT * FROM pg_indexes WHERE tablename = 'bs_dd_check_weight';
      register: result

    - name: Save result
      ansible.builtin.copy:
        content: "{{ result.query_result | to_nice_json }}"
        dest: /tmp/pg_indexs.json

    - ansible.builtin.fetch:
        src: /tmp/pg_indexs.json
        dest: ~/work/fetch_data/pg_indexs/{{ inventory_hostname }}.json
        flat: true