# ap clickhouse/city_monitor

- name: clickhouse
  hosts: y2
  gather_facts: no
  tasks:
    - shell: >
        clickhouse-client --password CZMP.2022 --port 9030 -nq 
        "select check_time, create_time from cityserver_dist.bs_dd_check_weight order by create_time desc limit 1;"
      register: ctime
    - name: last_time
      debug: msg="{{ ctime.stderr }} {{ ctime.stdout }}"
    - shell: >
        clickhouse-client --password CZMP.2022 --port 9030 -nq 
        "select count(*) from cityserver_dist.bs_dd_check_weight WHERE create_time > '2023-09-01 00:00:00';"
      register: count
    - name: clickhouse_count
      debug: msg="{{ count.stdout }}"

- name: postgresql
  hosts: y4
  gather_facts: no
  tasks:
    - postgresql_query:
        login_host: 127.0.0.1
        db: czmpcity
        login_password: CZMP^2023
        port: 5432
        query: SELECT count(*) from bs_dd_check_weight WHERE create_time > '2023-09-01 00:00:00';
      register: count
    - name: postgresql_count
      debug: msg="{{ count.query_result }}"

# now offset: 2239