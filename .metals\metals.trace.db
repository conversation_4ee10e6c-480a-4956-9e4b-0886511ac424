2024-02-10 12:44:57.551820+08:00 database: flush
org.h2.message.DbException: General error: "org.h2.mvstore.MVStoreException: The write format 2 is smaller than the supported format 3 [2.2.224/5]" [50000-224]
	at org.h2.message.DbException.get(DbException.java:212)
	at org.h2.message.DbException.convert(DbException.java:407)
	at org.h2.mvstore.db.Store.lambda$new$0(Store.java:122)
	at org.h2.mvstore.MVStore.handleException(MVStore.java:1546)
	at org.h2.mvstore.MVStore.panic(MVStore.java:371)
	at org.h2.mvstore.MVStore.<init>(MVStore.java:291)
	at org.h2.mvstore.MVStore$Builder.open(MVStore.java:2035)
	at org.h2.mvstore.db.Store.<init>(Store.java:133)
	at org.h2.engine.Database.<init>(Database.java:326)
	at org.h2.engine.Engine.openSession(Engine.java:92)
	at org.h2.engine.Engine.openSession(Engine.java:222)
	at org.h2.engine.Engine.createSession(Engine.java:201)
	at org.h2.engine.SessionRemote.connectEmbeddedOrServer(SessionRemote.java:343)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:125)
	at org.h2.Driver.connect(Driver.java:59)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:681)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:229)
	at scala.meta.internal.metals.Tables.upgradeIfNeeded(Tables.scala:180)
	at scala.meta.internal.metals.Tables.persistentConnection(Tables.scala:148)
	at scala.meta.internal.metals.Tables.tryAutoServer(Tables.scala:91)
	at scala.meta.internal.metals.Tables.connect(Tables.scala:55)
	at scala.meta.internal.metals.Tables.connection(Tables.scala:84)
	at scala.meta.internal.metals.Tables.$anonfun$fingerprints$1(Tables.scala:44)
	at scala.meta.internal.metals.Fingerprints.load(Fingerprints.scala:52)
	at scala.meta.internal.metals.MetalsLspService.$anonfun$loadFingerPrints$1(MetalsLspService.scala:864)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.concurrent.Future$.$anonfun$apply$1(Future.scala:687)
	at scala.concurrent.impl.Promise$Transformation.run(Promise.scala:467)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.h2.jdbc.JdbcSQLNonTransientException: General error: "org.h2.mvstore.MVStoreException: The write format 2 is smaller than the supported format 3 [2.2.224/5]" [50000-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:566)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	... 31 more
Caused by: org.h2.mvstore.MVStoreException: The write format 2 is smaller than the supported format 3 [2.2.224/5]
	at org.h2.mvstore.DataUtils.newMVStoreException(DataUtils.java:996)
	at org.h2.mvstore.FileStore.getUnsupportedWriteFormatException(FileStore.java:943)
	at org.h2.mvstore.FileStore.processCommonHeaderAttributes(FileStore.java:547)
	at org.h2.mvstore.RandomAccessStore.readStoreHeader(RandomAccessStore.java:227)
	at org.h2.mvstore.FileStore.start(FileStore.java:916)
	at org.h2.mvstore.MVStore.<init>(MVStore.java:289)
	... 25 more
2024-02-10 12:44:57.554222+08:00 database: opening /Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts/.metals/metals
org.h2.message.DbException: Unsupported database file version or invalid file header in file "/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts/.metals/metals.mv.db" [90048-224]
	at org.h2.message.DbException.get(DbException.java:212)
	at org.h2.mvstore.db.Store.convertMVStoreException(Store.java:158)
	at org.h2.mvstore.db.Store.<init>(Store.java:142)
	at org.h2.engine.Database.<init>(Database.java:326)
	at org.h2.engine.Engine.openSession(Engine.java:92)
	at org.h2.engine.Engine.openSession(Engine.java:222)
	at org.h2.engine.Engine.createSession(Engine.java:201)
	at org.h2.engine.SessionRemote.connectEmbeddedOrServer(SessionRemote.java:343)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:125)
	at org.h2.Driver.connect(Driver.java:59)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:681)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:229)
	at scala.meta.internal.metals.Tables.upgradeIfNeeded(Tables.scala:180)
	at scala.meta.internal.metals.Tables.persistentConnection(Tables.scala:148)
	at scala.meta.internal.metals.Tables.tryAutoServer(Tables.scala:91)
	at scala.meta.internal.metals.Tables.connect(Tables.scala:55)
	at scala.meta.internal.metals.Tables.connection(Tables.scala:84)
	at scala.meta.internal.metals.Tables.$anonfun$fingerprints$1(Tables.scala:44)
	at scala.meta.internal.metals.Fingerprints.load(Fingerprints.scala:52)
	at scala.meta.internal.metals.MetalsLspService.$anonfun$loadFingerPrints$1(MetalsLspService.scala:864)
	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.scala:18)
	at scala.concurrent.Future$.$anonfun$apply$1(Future.scala:687)
	at scala.concurrent.impl.Promise$Transformation.run(Promise.scala:467)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.h2.jdbc.JdbcSQLNonTransientConnectionException: Unsupported database file version or invalid file header in file "/Users/<USER>/Library/CloudStorage/OneDrive-个人/scripts/.metals/metals.mv.db" [90048-224]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:690)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	... 26 more
Caused by: org.h2.mvstore.MVStoreException: The write format 2 is smaller than the supported format 3 [2.2.224/5]
	at org.h2.mvstore.DataUtils.newMVStoreException(DataUtils.java:996)
	at org.h2.mvstore.FileStore.getUnsupportedWriteFormatException(FileStore.java:943)
	at org.h2.mvstore.FileStore.processCommonHeaderAttributes(FileStore.java:547)
	at org.h2.mvstore.RandomAccessStore.readStoreHeader(RandomAccessStore.java:227)
	at org.h2.mvstore.FileStore.start(FileStore.java:916)
	at org.h2.mvstore.MVStore.<init>(MVStore.java:289)
	at org.h2.mvstore.MVStore$Builder.open(MVStore.java:2035)
	at org.h2.mvstore.db.Store.<init>(Store.java:133)
	... 23 more
